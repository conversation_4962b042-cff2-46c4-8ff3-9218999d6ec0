<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\Admin\Models\Asociado;

class AsociadosFamiliares extends Model
{
    use HasFactory;
    protected $table = 'asociados_familiares';
    protected $fillable = [
        'asociado_idasociado',
        'familiares_id',
        'parentescos_id',
        'familiares_tipo_id',
    ];

    public function familiares()
    {
        return $this->belongsTo(Familiares::class, 'familiares_id', 'id');
    }

    public function asociados()
    {
        return $this->belongsTo(Asociado::class, 'asociado_idasociado', 'id');
    }

    public function parentesco()
    {
        return $this->belongsTo(Parentescos::class, 'parentescos_id', 'id');
    }

    public function tipoFamiliar()
    {
        return $this->belongsTo(FamiliaresTipo::class, 'familiares_tipo_id', 'id');
    }
}
