<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Deduccione
 * 
 * @property int $iddeduc
 * @property string $codigo_deduc
 * @property string $concepto
 * @property bool $condicion
 * @property bool $aplica
 * @property string $formula
 * @property float $monto_deduc
 * @property float $monto_fijo
 * @property string $codigo_auxil
 * @property string $codigo_cuenta
 *
 * @package App\Models
 */
class Deduccione extends Model
{
	protected $table = 'deducciones';
	protected $primaryKey = 'iddeduc';
	public $timestamps = false;

	protected $casts = [
		'condicion' => 'bool',
		'aplica' => 'bool',
		'monto_deduc' => 'float',
		'monto_fijo' => 'float'
	];

	protected $fillable = [
		'codigo_deduc',
		'concepto',
		'condicion',
		'aplica',
		'formula',
		'monto_deduc',
		'monto_fijo',
		'codigo_auxil',
		'codigo_cuenta'
	];
}
