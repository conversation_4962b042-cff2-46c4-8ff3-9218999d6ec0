<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class TasasDecambio
 * 
 * @property int $idtasa
 * @property Carbon $fecha_tasa
 * @property bool $tipo_tasa
 * @property float $tasa_am
 * @property float $tasa_pm
 * @property string $diasemana
 * @property string $descri_tasa
 *
 * @package App\Models
 */
class TasasDecambio extends Model
{
	protected $table = 'tasas_decambio';
	protected $primaryKey = 'idtasa';
	public $timestamps = false;

	protected $casts = [
		'fecha_tasa' => 'datetime',
		'tipo_tasa' => 'bool',
		'tasa_am' => 'float',
		'tasa_pm' => 'float'
	];

	protected $fillable = [
		'fecha_tasa',
		'tipo_tasa',
		'tasa_am',
		'tasa_pm',
		'diasemana',
		'descri_tasa'
	];
}
