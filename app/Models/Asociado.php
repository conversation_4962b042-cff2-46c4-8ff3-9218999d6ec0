<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Models\AsociadosFamiliares;
use App\Models\PrestamosDeasociado;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Asociado
 *
 * @property int $idasociado
 * @property string|null $nac
 * @property string|null $cedula_soc
 * @property string|null $apellidos
 * @property string|null $nombres
 * @property bool|null $condicion_soc
 * @property bool|null $estado_civil
 * @property bool|null $sexo
 * @property Carbon|null $fecha_ingreso
 * @property Carbon|null $fecha_nac
 * @property string|null $numero_exp
 * @property string|null $codigo_dep
 * @property bool|null $clase_pers
 * @property string|null $tipo_pers
 * @property string|null $codigo_nomina
 * @property float|null $porcen_apopers
 * @property float|null $porcen_apopatr
 * @property float|null $porcen_apoextrd
 * @property float|null $sueldo_base
 * @property float|null $ingresos_adic
 * @property float|null $monto_primas
 * @property float|null $saldah_persac
 * @property float|null $saldah_patrac
 * @property float|null $saldah_voluac
 * @property float|null $saldah_extrdac
 * @property float|null $saldah_perspe
 * @property float|null $saldah_patrpe
 * @property float|null $saldah_volupe
 * @property float|null $saldah_extrdpe
 * @property string|null $codigo_banco
 * @property string|null $numero_cuenta
 * @property string|null $numero_telefpers
 * @property string|null $numero_telefofi
 * @property string|null $numero_telefcont
 * @property string|null $cargo_delsocio
 * @property string|null $profesion
 * @property float|null $neto_nomina
 * @property string|null $direccion
 * @property string|null $codigo_estado
 * @property string|null $codigo_ciudad
 * @property string|null $email
 * @property string|null $codigo_dezona
 * @property bool|null $sw_caldivi
 * @property bool|null $sw_elector
 * @property bool|null $status_web
 * @property bool|null $socio_moroso
 * @property Carbon|null $fecha_retiro
 * @property Carbon|null $fecha_li
 * @property string|null $observaciones
 * @property float|null $sahpers_index
 * @property float|null $sahpatr_index
 * @property float|null $sahvolu_index
 * @property bool|null $condicion_afil
 * @property float|null $ayuda_mutua
 * @property Carbon|null $fecha_ayuda
 *
 * @package App\Models
 */
class Asociado extends Model
{
    protected $table = 'asociados';
    protected $primaryKey = 'idasociado';
    public $timestamps = false;



    protected $fillable = [
        'idasociado',
        'nac',
        'cedula_soc',
        'apellidos',
        'nombres',
        'condicion_soc',
        'estado_civil',
        'sexo',
        'fecha_ingreso',
        'fecha_nac',
        'numero_exp',
        'codigo_dep',
        'clase_pers',
        'tipo_pers',
        'codigo_nomina',
        'porcen_apopers',
        'porcen_apopatr',
        'porcen_apoextrd',
        'sueldo_base',
        'ingresos_adic',
        'monto_primas',
        'saldah_persac',
        'saldah_patrac',
        'saldah_voluac',
        'saldah_extrdac',
        'saldah_perspe',
        'saldah_patrpe',
        'saldah_volupe',
        'saldah_extrdpe',
        'codigo_banco',
        'numero_cuenta',
        'numero_telefpers',
        'numero_telefofi',
        'numero_telefcont',
        'cargo_delsocio',
        'profesion',
        'neto_nomina',
        'direccion',
        'codigo_estado',
        'codigo_ciudad',
        'email',
        'codigo_dezona',
        'sw_caldivi',
        'sw_elector',
        'status_web',
        'socio_moroso',
        'fecha_retiro',
        'fecha_li',
        'observaciones',
        'sahpers_index',
        'sahpatr_index',
        'sahvolu_index',
        'condicion_afil',
        'ayuda_mutua',
        'fecha_ayuda',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'asociado_idasociado');
    }

    public function grupoFamiliar()
    {
        return $this->hasMany(AsociadosFamiliares::class, 'asociado_idasociado', 'idasociado')->with('familiares', 'parentesco', 'tipoFamiliar')->orderBy('familiares_tipo_id', 'asc');
    }

    public function autorizado()
    {
        return $this->hasOne(ServicioFunerarioPersonasAutorizadas::class, 'asociado_idasociado', 'idasociado');
    }

    public function prestamosYFinanciamientos($soporte = null)
    {
        return $this->hasMany(PrestamosDeasociado::class, 'cedula_soc', 'cedula_soc')
            ->when($soporte, function ($q) use ($soporte) {

                $q->where('soporte_fi', $soporte);
            });
    }

    public function prestamos()
    {
        return $this->hasMany(PrestamosDeasociado::class, 'cedula_soc', 'cedula_soc');
    }
    public function condicion()
    {
        return $this->belongsTo(ConfigTools::class, 'condicion_soc', 'codigo')
            ->where('modulo', 'asociados')
            ->where('referencia', 'estatus');
    }

    public function tipoPersonal()
    {

        return $this->belongsTo(CodigosVario::class, 'tipo_pers', 'codigo_v')
            ->where('ref_v', 'P');
    }

    public function ubicacion()
    {
        return $this->belongsTo(CodigosVario::class, 'codigo_dep', 'codigo_v')
            ->where('ref_v', 'D');
    }

    public function contrato()
    {
        return $this->hasMany(Contrato::class, 'asociado_idasociado', 'idasociado');
    }
}
