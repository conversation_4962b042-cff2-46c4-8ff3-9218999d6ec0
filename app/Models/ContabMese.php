<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ContabMese
 * 
 * @property int $idmes
 * @property string $periodo_cont
 * @property int $mescont
 * @property string $nombre_mes
 * @property bool $condic_mes
 * @property string $observaciones
 *
 * @package App\Models
 */
class ContabMese extends Model
{
	protected $table = 'contab_meses';
	protected $primaryKey = 'idmes';
	public $timestamps = false;

	protected $casts = [
		'mescont' => 'int',
		'condic_mes' => 'bool'
	];

	protected $fillable = [
		'periodo_cont',
		'mescont',
		'nombre_mes',
		'condic_mes',
		'observaciones'
	];
}
