<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ContabMovimiento
 * 
 * @property int $idmovcont
 * @property string $periodo_cont
 * @property int $idcomprob
 * @property string $codigo_auxil
 * @property string $codigo_cuenta
 * @property string $concepto
 * @property string $tipodoc
 * @property string $numerodoc
 * @property float $monto_db
 * @property float $monto_cr
 * @property bool $condic_mov
 *
 * @package App\Models
 */
class ContabMovimiento extends Model
{
	protected $table = 'contab_movimientos';
	protected $primaryKey = 'idmovcont';
	public $timestamps = false;

	protected $casts = [
		'idcomprob' => 'int',
		'monto_db' => 'float',
		'monto_cr' => 'float',
		'condic_mov' => 'bool'
	];

	protected $fillable = [
		'periodo_cont',
		'idcomprob',
		'codigo_auxil',
		'codigo_cuenta',
		'concepto',
		'tipodoc',
		'numerodoc',
		'monto_db',
		'monto_cr',
		'condic_mov'
	];
}
