<?php

namespace App\Exports;

use App\Models\Contrato;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class ServicioFunerarioExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $selectedRows;
    protected $filters;

    public function __construct($selectedRows = null, $filters = null)
    {
        $this->selectedRows = $selectedRows;
        $this->filters = $filters;
    }

    public function collection()
    {
        if ($this->selectedRows && count($this->selectedRows) > 0) {
            return Contrato::whereIn('id', $this->selectedRows)
                ->with(['asociado', 'asociado.ubicacion', 'condicion'])
                ->get();
        } else {
            $query = Contrato::query()
                ->join('config_tools', 'servicio_funerario_contratos.estatus', '=', 'config_tools.codigo')
                ->where('config_tools.modulo', 'servicio-funerario.contratos')
                ->where('config_tools.referencia', 'estatus')
                ->with(['asociado', 'asociado.ubicacion', 'condicion']);

            // Aplicar filtros si existen
            if ($this->filters) {
                if (isset($this->filters['estatus']) && $this->filters['estatus'] !== '') {
                    $query->where('servicio_funerario_contratos.estatus', $this->filters['estatus']);
                }
                
                if (isset($this->filters['periodo']) && $this->filters['periodo'] !== '') {
                    $query->where('servicio_funerario_contratos.periodo', $this->filters['periodo']);
                }
                
                if (isset($this->filters['fecha_desde']) && $this->filters['fecha_desde'] !== '') {
                    $query->whereDate('servicio_funerario_contratos.created_at', '>=', $this->filters['fecha_desde']);
                }
                
                if (isset($this->filters['fecha_hasta']) && $this->filters['fecha_hasta'] !== '') {
                    $query->whereDate('servicio_funerario_contratos.created_at', '<=', $this->filters['fecha_hasta']);
                }
            }

            return $query->get();
        }
    }

    public function headings(): array
    {
        return [
            'ID',
            'Cédula',
            'Periodo',
            'Nombres',
            'Apellidos',
            'Ubicación',
            'Monto Total',
            'Estatus',
            'Fecha de Creación',
        ];
    }

    public function map($contrato): array
    {
        return [
            $contrato->id,
            $contrato->cedula_soc,
            $contrato->periodo,
            $contrato->asociado->nombres ?? 'N/A',
            $contrato->asociado->apellidos ?? 'N/A',
            $contrato->asociado->ubicacion->descri_v ?? 'N/A',
            number_format($contrato->monto_total_contrato, 2),
            $contrato->condicion->descripcion ?? 'N/A',
            $contrato->created_at->format('d/m/Y'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}