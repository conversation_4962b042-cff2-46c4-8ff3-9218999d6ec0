<div>
    @if ($encuestas)
    <x-button class="btn btn-sm btn-dark align-items-center" wire:click="$set('encuestaModal', true)">
        Completar
        Encuesta
    </x-button>

    <x-dialog-modal wire:model="encuestaModal" maxWidth="xl">

        <x-slot name="title">
            {{ __('Complete la encuesta') }}
        </x-slot>
        <x-slot name="content">
            <div>
                <div class="container">
                    <form wire:submit.prevent="save">
                        <h1>{{ $encuestas->titulo }}.</h1>
                        <p>{{ $encuestas->descripcion }}</p>

                        @foreach ($encuestas->preguntas as $key => $pregunta)
                        <div class="">
                            <div class="card-header">
                                <section class="row">
                                    <h3 class="card-title"><b class="">Pregunta {{ $key + 1 }}:</b>
                                        {{ $pregunta->pregunta }}</h3>
                                    <p></p>
                                </section>
                            </div>

                            <div class="callout callout-info">
                                <section class="row">
                                    @foreach ($pregunta->opciones()->get() as $key => $opciones)
                                    <div class="col-md-12">
                                        <section class="row">

                                            @if ($pregunta->tipo == 'textarea')
                                            <textarea class="form-control" rows="3"
                                                placeholder="{{ $opciones->opcion }}"
                                                name="opciones.{{ $pregunta->id }}.{{ $opciones->id }}"
                                                wire:model="opciones.{{ $pregunta->id }}.{{ $opciones->id }}"></textarea>
                                            @elseif($pregunta->tipo == 'radio')
                                            <div class="col-10">
                                                <p>{{ $key + 1 }}- {{ $opciones->opcion }}</p>
                                            </div>

                                            <div class="col-2">
                                                <x-input type="{{ $pregunta->tipo }}"
                                                    name="opciones.{{ $pregunta->id }}" value="{{ $opciones->id }}"
                                                    wire:model="opciones.{{ $pregunta->id }}" />
                                            </div>
                                            @elseif($pregunta->tipo == 'number')
                                            <div class="col-10">
                                                <p>{{ $key + 1 }}- {{ $opciones->opcion }}</p>
                                            </div>

                                            <div class="col-2">
                                                <x-input type="{{ $pregunta->tipo }}"
                                                    name="opciones.{{ $pregunta->id }}[]" value="{{ $opciones->id }}"
                                                    wire:model="opciones.{{ $pregunta->id }}.{{ $opciones->id }}"
                                                    min="1" max="5" required />
                                            </div>
                                            @elseif($pregunta->tipo == 'checkbox')
                                            <div class="col-10">
                                                <p>{{ $key + 1 }}- {{ $opciones->opcion }}</p>
                                            </div>

                                            <div class="col-2">
                                                <x-input type="{{ $pregunta->tipo }}"
                                                    name="opciones.{{ $pregunta->id }}[]" value="{{ $opciones->id }}"
                                                    wire:model="opciones.{{ $pregunta->id }}.{{ $opciones->id }}" />
                                            </div>
                                            @endif
                                        </section>
                                    </div>
                                    @endforeach
                                    <x-input-error for="opciones.{{ $pregunta->id }}" />
                                </section>
                            </div>
                        </div>
                        @endforeach
                        <x-button class="btn btn-sm btn-primary align-items-center btn-block"
                            wire:submit.prevent="save">
                            Guardar
                        </x-button>
                    </form>
                </div>
            </div>
        </x-slot>
        <x-slot name="footer">

        </x-slot>
    </x-dialog-modal>
    @endif
</div>