<div>
    <section class="content">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Préstamos</h3>
            </div>
            <div class="card-body">
                <div
                    class="d-flex justify-content-between flex-xl-row flex-md-column flex-sm-row flex-column p-sm-3sss p-0">
                    <div class="mb-xl-0 mb-4">
                        <div class="d-flex svg-illustration mb-3 gap-2">
                            <span class="app-brand-logo demo">
                                <span
                                    style="display: inline-block; margin: 0.00px 0.00px; border: 0.00px solid #000000; transform: rotate(0.00rad) translateZ(0px); -webkit-transform: rotate(0.00rad) translateZ(0px); width: 136.96px; height: 60.00px;"><img
                                        alt=""
                                        src="{{ asset('assets/img/logo/' . config('app.name') . '.png') }}"
                                        style="height: 60.00px; margin-left: 0.00px; margin-top: 0.00px; transform: rotate(0.00rad) translateZ(0px); -webkit-transform: rotate(0.00rad) translateZ(0px);"
                                        title="">
                                </span>
                            </span>
                        </div>
                        <p class="mb-0">{{ empresa()->nombre_caja }}</p>
                        <p class="mb-0">{{ empresa()->sector_publico }}</p>
                        <p class="mb-0">Rif:{{ empresa()->numero_rif }}</p>
                    </div>
                    <div>
                        <h4>Asociado #{{ auth()->user()->asociado->idasociado }}</h4>
                        <div class="mb-0">
                            <span class="me-0">Actualizado al: </span>
                            <span class="fw-medium">{{ date('d/m/Y') }}</span>
                        </div>
                        <div>
                            <span class="me-0">Fecha de emisión:</span>
                            <span class="fw-medium">{{ date('d/m/Y') }}</span>
                        </div>
                    </div>
                </div>
                <!-- Header -->

                <div class="table-responsive">
                    <table class="table table-bordered text-1 table-sm table-striped">
                        <thead>
                            <tr>
                                <td colspan="4">
                                    <span class="fw-600">Detalles del préstamo</span>:
                                    <span class="float-end justify-content-center text-center float-right"></span>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="fw-600"><b>Codigo:</b></td>
                                <td>{{ $detallePrestamo->controlDePrestamo->codigo_prest }}</td>
                            </tr>
                            <tr>
                                <td class="fw-600"><b>Nombre del préstamo:</b></td>
                                <td>{{ $detallePrestamo->controlDePrestamo->descripcion }}</td>
                            </tr>
                            <tr>
                                <td class="fw-600"><b>Tipo de préstamo:</b></td>
                                <td>{{ $detallePrestamo->controlDePrestamo->tipo_prestamo }}</td>
                            </tr>
                            <tr>
                                <td class="fw-600"><b>Clase de préstamo:</b></td>
                                <td>{{ $detallePrestamo->controlDePrestamo->clase_prest }}</td>
                            </tr>
                            <tr>
                                <td class="fw-600"><b>Tasa interés:</b></td>
                                <td>{{ $detallePrestamo->controlDePrestamo->tasa_i }}%
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- CUOTAS NORMALES -->
                    <h4 class="text-4 mt-2 text-center">
                        {{ $detallePrestamo->controlDePrestamo->soporte_fi == 2 ? 'Pŕestamo Ordinario' : 'Pŕestamo Financiado' }}
                    </h4>
                    <hr>
                    <h4 class="text-4 mt-2">
                        CUOTAS NORMALES
                    </h4>
                    <div class="table-responsive">
                        <table class="table table-bordered text-1 table-sm">
                            <thead>
                                <tr class="bg-light">
                                    <th>#</th>
                                    <th>CONCEPTO</th>
                                    <th>FECHA</th>
                                    <th>MONTO CUOTA</th>
                                    <th>TASA</th>
                                    <th>PAGADO</th>
                                    <th>SALDO</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($detallePrestamo->controlDePrestamo->prestamosRelacionDecuotas->where('cedula_soc',auth()->user()->asociado->cedula_soc)->where('origen_delpago','!=',0) as $detalle)
                                    {{-- indexado --}}
                                    <tr>
                                        <td><b>{{ $loop->iteration }}</b></td>
                                        <td>{{ $detalle->concepto }}</td>
                                        <td>{{ date('d/m/Y', strtotime($detalle->fecha_pago)) }}</td>
                                        <td>{{ $detalle->condic_pagocn == 1 ? $detalle->monto_cuota : $detalle->monto_cuotai }}
                                        </td>
                                        <td>{{ $detalle->tasa_depago }}</td>
                                        <td>{{ $detalle->condic_pagocn == 1 ? $detalle->monto_pago : $detalle->monto_pago }}
                                        </td>
                                        {{-- <td>{{ $detalle->condic_pagocn == 1 ? $detalle->saldo_prest : $detalle->saldoprest_index }}
                                        </td> --}}
                                        <td>{{ $detalle->saldo_prest }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6">
                                            <i class="fas fa-info-circle"></i> No hay registros
                                        </td>
                                    </tr>
                                @endforelse

                            </tbody>
                        </table>
                    </div>
                    <!-- FIN CUOTAS NORMALES -->

                    <!-- CUOTAS ESPECIALES -->
                    <h4 class="text-4 mt-2">
                        CUOTAS ESPECIALES
                    </h4>
                    <div class="table-responsive">
                        <table class="table table-bordered text-1 table-sm">
                            <thead>
                                <tr class="bg-light">
                                    <th>#</th>
                                    <th>FECHA</th>
                                    <th>MONTO CUOTA</th>
                                    <th>TASA</th>
                                    <th>PAGADO</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($detallePrestamo->prestamosRelacionDecuotasEspeciales->where('cedula_soc',auth()->user()->asociado->cedula_soc)->where('condic_cesp',1) as $detalle)
                                    {{-- indexado --}}
                                    <tr>
                                        <td><b>{{ $loop->iteration }}</b></td>
                                        <td>{{ date('d/m/Y', strtotime($detalle->fecha_depago)) }}</td>
                                        <td>{{ $detalle->condic_pagocn == 1 ? $detalle->monto_cesp : $detalle->monto_cespindex }}
                                        </td>
                                        <td>{{ $detalle->tasa_index }}</td>
                                        <td>{{ $detalle->creditos }}</td>
                                    </tr>

                                @empty
                                    <tr>
                                        <td colspan="6">
                                            <i class="fas fa-info-circle"></i> No hay registros
                                        </td>
                                    </tr>
                                @endforelse

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- Footer -->
            <footer class="text-center">
                <hr>
                <p>{{ empresa()->direccion }}<br>Tlf. {{ empresa()->telefonos }}<br>Página
                    web:{{ empresa()->sitio_web }}</p>
            </footer>
        </div>
    </section>
</div>
