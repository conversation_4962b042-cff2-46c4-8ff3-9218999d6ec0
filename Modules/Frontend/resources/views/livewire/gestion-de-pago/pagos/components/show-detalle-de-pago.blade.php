<div>


    <div class="section">
        <div class="row invoice-info">

            <div class="col-sm-8 invoice-col">
                <h4> <i class="fas fa-globe"></i> {{ empresa()->siglas_caja }}</h4>
                <address>
                    {{ empresa()->direccion }}
                </address>
            </div>

            <div class="col-sm-4 invoice-col">
                <b>Orden ID:</b> #{{ $pago->id }}<br>
                <b>Fecha de pago:</b> {{ $pago->fecha_pago }}<br>
                <b>
                    <span class="invoice-status badge badge-lg badge-{{ $pago->condicion->color }}">
                        <i class="{{ $pago->condicion->icono }}"></i>
                        {{ $pago->condicion->descripcion }}
                    </span>
                </b>
            </div>
        </div>

        <h5>Detalle de pago</h5>
        <div class="table-responsive">
            <table class="table table-condensed table-hover">
                <thead>
                    <tr>
                        <th>Concepto</th>
                        <th>Referencia</th>
                        <th class="text-center">Banco</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ $pago->concepto->descripcion }}</td>
                        <td>{{ $pago->referencia }}</td>
                        <td>{{ $pago->banco->codigo_banco }}-{{ $pago->banco->descripcion }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="row">

            <div class="col-6">

            </div>

            <div class="col-6">

                <div class="table-responsive">
                    <table class="table table-condensed table-hover">
                        <tbody>
                            <tr>
                                <th style="width:50%">Subtotal:</th>
                                <td>Bs. {{ $pago->monto }}</td>
                            </tr>
                            <tr>
                                <th>Total:</th>
                                <td>Bs. {{ $pago->monto }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
    <hr>
    <div class="p-3 section">
        <div class="row">
            <ul class="clearfix mailbox-attachments d-flsex align-items-stretch">
                @forelse ($pago->media as $media)
                    <li>

                        <div class="mailbox-attachment-info">

                            <a target="_blank" href="{{ asset($media->file_path) }}" data-toggle="lightbox"
                                data-max-width="600" data-gallery="gallery" class="mailbox-attachment-name">
                                @if (pathinfo($media->file_path, PATHINFO_EXTENSION) == 'pdf')
                                    <i class="fas fa-file-pdf"></i>
                                @elseif(pathinfo($media->file_path, PATHINFO_EXTENSION) == 'jpg' ||
                                        pathinfo($media->file_path, PATHINFO_EXTENSION) == 'jpeg' ||
                                        pathinfo($media->file_path, PATHINFO_EXTENSION) == 'png' ||
                                        pathinfo($media->file_path, PATHINFO_EXTENSION) == 'gif')
                                    <img class="img-fluid" src="{{ asset($media->file_path) }}">
                                @else
                                    <i class="fas fa-file-alt"></i>
                                @endif
                            </a>
                            <span class="clearfix mt-1 mailbox-attachment-size">
                                <span>{{ number_format($media->size / 1024, 2) }} kb</span>
                                <x-button wire:click="download({{ $media->id }})"
                                    class="float-right btn btn-default btn-sm"> <i
                                        class="fas fa-cloud-download-alt"></i>
                                </x-button>
                            </span>

                        </div>
                    </li>
                @empty
                    <h1>No tiene imagen cargada</h1>
                @endforelse
            </ul>
        </div>
    </div>

    <hr>
    <div class="float-right">
        @if ($pago->estatus == 1)
            <livewire:frontend::gestion-de-pago.pagos.pages.destroy :pago="$pago" />
        @endif
    </div>

</div>
