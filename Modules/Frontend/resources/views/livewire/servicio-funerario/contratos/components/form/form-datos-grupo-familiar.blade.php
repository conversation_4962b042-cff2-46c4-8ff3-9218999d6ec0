<div>
    <div class="card-header">
        <div class="row">
            <div class="col-12">
                <h6><i class="fas fa-banks"></i><b> GRUPO FAMILIAR</b></h6>
                <h6 class="card-subtitle text-muted">Listado de personas en su carga familiar.
                </h6>
                @if (!$autorizados->autorizado)
                    <div class="alert alert-warning" role="alert">
                        <h4 class="alert-heading"><i class="fa fa-info-circle"></i> Atención .</h4>
                        <p>Antes de continuar, es importante que agregue al menos una persona de contacto. Esta persona
                            será la primera en ser contactada en caso de que ocurra un suceso. Puede agregar a alguien que ya pertenezca a su grupo familiar o a una
                            persona externa, como un amigo o familiar cercano. <br><b>(Es obligatorio agregar al menos una persona)</b>
                        </p>

                        <p class="mb-0"></p>
                    </div>
                @endif
                <div class="col-s">
                    <livewire:backend::familiares.pages.create :asociado="$asociado" />
                    @if (!$autorizados->autorizado)
                        <livewire:frontend::servicio-funerario.contratos.components.form.form-persona-autorizada
                            :asociado="$asociado" />
                    @endif
                </div>
            </div>
        </div>
    </div>
    @if ($familiares->grupoFamiliar->count() > 0)
        <div class="card-body">
            @if (session()->has('message'))
                <div class="alert alert-success"><i class="fas fa-info-circle"></i>
                    {{ session('message') }}</div>
            @endif
            @if (session()->has('error'))
                <div class="alert alert-warning"><i class="fas fa-info-circle"></i>
                    {{ session('error') }}</div>
            @endif

            <div class="row">
                <div class="col-6 border-right text-dark">
                    <div class="description-block">
                        <i class="fas fa-lg fa-users"></i>
                        <h5 class="description-header">
                            Carga Básica
                        </h5>
                        <p class="description-text">
                            <span class="badge bg-primary">
                                {{ $cargaBasica }}
                            </span>
                        </p>
                    </div>
                </div>
                <div class="col-6 text-dark">
                    <div class="description-block">
                        <i class="fas fa-lg fa-users"></i>
                        <h5 class="description-header">
                            Carga Adicional
                        </h5>
                        <p class="description-text">
                            <span class="badge bg-primary">
                                {{ $cargaAdicional }}
                            </span>
                        </p>
                    </div>
                </div>

                <div class=" col-12">
                    <table class="table table-responsive-md table-sm table-hover" wire:loading.remove>
                        <thead>
                            <tr>
                                <th>#</th>
                                <th style="">CÉDULA</th>
                                <th style="">NOMBRES Y APELLIDOS</th>
                                <th class="d-none d-md-table-cell" style="width:25%">PARENTESCO</th>
                                <th style="">EDAD</th>
                                <th style="">ACCIONES</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($familiares->grupoFamiliar as $key => $familiar)
                                @if ($familiar)
                                    <tr>
                                        <td>{{ $loop->iteration }}</td>
                                        <td>{{ $familiar->familiares->cedula }}</td>
                                        <td>{{ $familiar->familiares->apellidos }}
                                            {{ $familiar->familiares->nombres }}
                                        </td>
                                        <td>
                                            <select
                                                wire:change="changeParentesco({{ $familiar }},$event.target.value)"
                                                class="form-control">
                                                @foreach ($parentescos as $key => $parentesco)
                                                    <option
                                                        {{ $familiar->parentesco->id == $parentesco->id ? 'selected=selected' : '' }}
                                                        value="{{ $parentesco->id }}">
                                                        {{ $parentesco->descripcion }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </td>
                                        <td> {{ age($familiar->familiares->fecha_nac) }}</td>
                                        <td>
                                            <x-button class="btn btn-sm btn-green align-items-center"
                                                wire:click="modalEditFamiliar({{ $familiar->familiares->id }},{{ $familiar->asociado_idasociado }})">
                                                <i class="fa fa-edit"></i>
                                            </x-button>

                                            <x-button class="btn btn-sm btn-danger align-items-center"
                                                wire:click="modalDestroyFamiliar({{ $familiar->familiares->id }},{{ $familiar->asociado_idasociado }})">
                                                <i class="fa fa-trash"></i>
                                            </x-button>

                                            <x-button class="btn btn-sm btn-info align-items-center"
                                                wire:click="modalUploadDocumentos({{ $familiar->familiares->id }},{{ $familiar->asociado_idasociado }})">
                                                <i class="fa fa-upload"></i> Cargar Documentos
                                            </x-button>
                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                        </tbody>
                    </table>
                    <livewire:backend::familiares.pages.edit :asociado="$asociado" />
                    <livewire:backend::familiares.pages.destroy :asociado="$asociado" />
                </div>
            </div>
        </div>
    @else
        <div class="text-center fa-3x text-muted">
            <i class="fas fa-users"></i>
            <p class="text-lg">No hay familiares registrados</p>
        </div>
    @endif

    @if ($autorizados->autorizado)
        <div class=" col-12">
        <div class="card-body">
            <legend class="mt-2 mb-0 text-primary">Persona de contacto</legend>
            <div class=" col-12">
                <table class="table table-responsive-md table-sm table-hover" wire:loading.remove>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th style="">CÉDULA</th>
                            <th style="">NOMBRES Y APELLIDOS</th>
                            <th class="d-none d-md-table-cell" style="width:25%">PARENTESCO</th>
                            <th style="">EDAD</th>
                            <th style="">ACCIONES</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ $autorizados->autorizado->id }}</td>
                            <td>{{ $autorizados->autorizado->cedula }}</td>
                            <td>{{ $autorizados->autorizado->nombres }} {{ $autorizados->autorizado->apellidos }}
                            </td>
                            <td>
                                <select
                                    wire:change="changeParentesco({{ $autorizados->autorizado }},$event.target.value)"
                                    class="form-control">
                                    @foreach ($parentescos as $key => $parentesco)
                                        <option
                                            {{ $autorizados->autorizado->parentescos_id == $parentesco->id ? 'selected=selected' : '' }}
                                            value="{{ $parentesco->id }}">
                                            {{ $parentesco->descripcion }}
                                        </option>
                                    @endforeach
                                </select>
                            </td>
                            <td> {{ age($autorizados->autorizado->fecha_nac) }}</td>
                            <td>

                                <livewire:backend::servicio-funerario.autorizados.pages.edit :autorizado="$autorizados->autorizado" />

                                <x-button class="btn btn-sm btn-danger align-items-center"
                                    wire:click="modalDestroyAutorizado({{ $autorizados->autorizado->id }},{{ $autorizados->autorizado->asociado_idasociado }})">
                                    <i class="fa fa-trash"></i>
                                </x-button>
                            </td>
                        </tr>

                        {{-- @empty
                        <tr>
                            <td colspan="5">No hay autorizados registrados</td>
                        </tr>
                    @endforelse --}}
                    </tbody>
                </table>
            </div>
        </div>
            <div class="card-footer">
                <x-button class="float-right btn btn-primary" wire:click="save"> <i class="fas fa-save"></i>
                    {{ __('Guardar') }}
                </x-button>
            </div>

            </div>
    @endif

    <livewire:backend::servicio-funerario.autorizados.pages.destroy />

    <livewire:frontend::servicio-funerario.contratos.components.form.form-cargar-documentos :asociado="$asociado"/>


</div>
