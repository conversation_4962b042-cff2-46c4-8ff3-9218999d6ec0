<?php

use App\Http\Middleware\CheckUser;
use Illuminate\Support\Facades\Route;
use Modules\Frontend\Livewire\Familiares\Pages\Index;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

Route::prefix('/dashboard/familiares')
    ->middleware(CheckUser::class)
    ->group(function () {
        Route::get('/', Index::class)->name('dashboard.familiares.index');
    });
