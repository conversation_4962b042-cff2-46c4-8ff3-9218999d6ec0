<?php

namespace Modules\Frontend\Livewire\ServicioFunerario\Contratos\Components\Form;

use Carbon\Carbon;
use Livewire\Component;
use App\Models\Asociado;
use App\Models\Contrato;
use Illuminate\Support\Facades\DB;
use App\Models\AsociadosFamiliares;
use Illuminate\Support\Facades\Auth;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use App\Models\ServicioFunerarioContratosFamiliares;

class FormDetallesContrato extends Component
{
    use LivewireAlert;

    public $periodoActivo;
    public $carga_basica;
    public $carga_adicional;
    public $precioBasica;
    public $precioAdicional;
    public $variables;
    public $totales;
    public $confirmGenerarContrato = false;
    public $app_name;
    public function getListeners()
    {
        return [
            'confirmed',
            'refresh',
        ];
    }

    public function mount()
    {

        $this->app_name = config('app.name');
        //obtener periodo activo
        $fechaActual = Carbon::now()->format('Y-m-d');
        $this->periodoActivo = DB::table('apertura_procesos')->whereDate('fecha_desde', '<=', $fechaActual)
            ->whereDate('fecha_hasta', '>=', $fechaActual)
            ->where('codigo_v', 'SF')
            ->first()->periodo;

        if ($this->app_name == 'CAYPUEZ') {
            $this->caypuezFormula();
        } elseif ($this->app_name == 'CAPROF_UNELLEZ') {
            $this->caprofFormula();
        }
    }

    public function caypuezFormula()
    {
        //obtengo numero de carga basica
        $cargaBasica = AsociadosFamiliares::where('asociado_idasociado', auth()->user()->asociado_idasociado)
            ->where('familiares_tipo_id', 1)
            ->count();
        //obtengo numero de carga adicional
        $cargaAdicional = AsociadosFamiliares::where('asociado_idasociado', auth()->user()->asociado_idasociado)
            ->where('familiares_tipo_id', 2)
            ->count();

        //consulto la formula a usar
        $formula = DB::table('servicio_funerario_formulas')->where('nombre', 'CAYPUEZ')->value('formula');

        //consulto las variables de la formula
        $this->variables = DB::table('servicio_funerario_formulas_variables')->where('formulas_id', 2)->pluck('valor', 'variable')->toArray();


        // Reemplazar las variables en la fórmula con sus valores correspondientes
        foreach ($this->variables as $nombre => $valor) {
            if (!isset($$nombre)) { // Verificar si la variable ya tiene un valor asignado
                $$nombre = $valor;  // Asignar el valor de la base de datos si la variable no tiene un valor asignado
            }
            $formula = str_replace("{{$nombre}}", $$nombre, $formula);
        }
        $resultado = eval("return $formula;");

        // echo "Precio carga basica: $precioCargaBasica \n<br>";
        // echo "Precio de carga adicional: $precioCargaAdicional \n<br>";
        // echo "Familiares carga basica: $cargaBasica \n<br>";
        // echo "Familiares carga Adicional: $cargaAdicional \n<br>";
        // Mostrar el resultado
        // echo $formula . "\n<br>";
        // echo $resultado;

        //arreglo de variables
        $this->totales = array(
            'precioCargaBasica' => $precioCargaBasica,
            'precioCargaAdicional' => $precioCargaAdicional,
            'cargaBasica' => $cargaBasica,
            'cargaAdicional' => $cargaAdicional,
            'totalCargaBasica'  => $precioCargaBasica,
            'totalCargaAdicional' => $precioCargaAdicional * $cargaAdicional,
            'numeroCn' => $numeroCn,
            'numeroCesp' => $numeroCesp,
            'total' => $resultado
        );
    }

    public function generarContratoCAYPUEZ()
    {
        $this->caypuezFormula();
        // DB::table('servicio_funerario_contratos')->where('asociado_idasociado', auth()->user()->asociado_idasociado)
        //     ->where('periodo', $this->periodoActivo)->delete();

        //verifico si exite un contrato previo
        $contrato = Contrato::where('asociado_idasociado', auth()->user()->asociado_idasociado)->where('periodo', $this->periodoActivo)->first();
        //actualizo el contrato
        if ($contrato) {
            $contrato->update([
                'numero_cb' => $this->totales['cargaBasica'],
                'monto_cb' => $this->totales['precioCargaBasica'],
                'numero_ca' => $this->totales['cargaAdicional'],
                'monto_ca' => $this->totales['totalCargaAdicional'],
                'numero_cn' => $this->totales['numeroCn'],
                'monto_cn' => $this->totales['totalCargaAdicional'] / $this->totales['numeroCn'],
                'numero_cesp' => $this->totales['numeroCesp'],
                'monto_cesp' => $this->totales['precioCargaBasica'] / $this->totales['numeroCesp'],
                'monto_total_contrato' => $this->totales['total'],
                'estatus' => 1,
                'fecha' => date('Y-m-d'),
                'usuario' => auth()->user()->id,
                'anulado_por' => null,
                'fecha_anulacion' => null,

            ]);

            //ahora copio el grupo familiar y lo registro con el nuevo id de contrato y periodo
            $asociadosFamiliares = AsociadosFamiliares::where('asociado_idasociado', auth()->user()->asociado_idasociado)->get();

            if ($asociadosFamiliares->count() > 0) {
                foreach ($asociadosFamiliares as $asociadosFamiliar) {
                    $familiar = ServicioFunerarioContratosFamiliares::where('contrato_id', $contrato->id)
                        ->where('periodo', $this->periodoActivo)
                        ->where('asociado_idasociado', $asociadosFamiliar->asociado_idasociado)
                        ->where('familiares_id', $asociadosFamiliar->familiares_id)
                        ->where('parentescos_id', $asociadosFamiliar->parentescos_id)
                        ->where('familiares_tipo_id', $asociadosFamiliar->familiares_tipo_id)
                        ->first();

                    if ($familiar) {
                        $familiar->update([
                            'contrato_id' => $contrato->id,
                            'periodo' => $this->periodoActivo
                        ]);
                    } else {
                        $familiar = new ServicioFunerarioContratosFamiliares();
                        $familiar->asociado_idasociado = $asociadosFamiliar->asociado_idasociado;
                        $familiar->familiares_id = $asociadosFamiliar->familiares_id;
                        $familiar->parentescos_id = $asociadosFamiliar->parentescos_id;
                        $familiar->familiares_tipo_id = $asociadosFamiliar->familiares_tipo_id;
                        $familiar->contrato_id = $contrato->id;
                        $familiar->periodo = $this->periodoActivo;
                        $familiar->save();
                    }
                }
            }
        } else {
            //si no exite lo creo nuevo el contrato
            $contrato = new Contrato();
            $contrato->asociado_idasociado  = auth()->user()->asociado_idasociado;
            $contrato->cedula_soc           = auth()->user()->asociado->cedula_soc;
            $contrato->periodo              = $this->periodoActivo;
            $contrato->numero_cb            = $this->totales['cargaBasica'];
            $contrato->monto_cb             = $this->totales['precioCargaBasica'];
            $contrato->numero_ca            = $this->totales['cargaAdicional'];
            $contrato->monto_ca             = $this->totales['totalCargaAdicional'];
            $contrato->numero_cn            = $this->totales['numeroCn'];
            $contrato->monto_cn             = $this->totales['totalCargaAdicional'] / $this->totales['numeroCn'];
            $contrato->numero_cesp          = $this->totales['numeroCesp'];
            $contrato->monto_cesp           = $this->totales['precioCargaBasica'] / $this->totales['numeroCesp'];
            $contrato->monto_total_contrato = $this->totales['total'];
            $contrato->estatus              = 1;
            $contrato->fecha = date('Y-m-d');
            $contrato->usuario = auth()->user()->id;
            $contrato->save();

            //grupo familiar
            //auth()->user()->asociado->grupoFamiliar
            $asociadosFamiliares = AsociadosFamiliares::where('asociado_idasociado', auth()->user()->asociado_idasociado)->get();


            //ahora copio el grupo familiar y lo registro con el nuevo id de contrato y periodo

            if ($asociadosFamiliares->count() > 0) {
                foreach ($asociadosFamiliares as $asociadosFamiliar) {
                    $familiar = new ServicioFunerarioContratosFamiliares();
                    $familiar->asociado_idasociado = $asociadosFamiliar->asociado_idasociado;
                    $familiar->familiares_id = $asociadosFamiliar->familiares_id;
                    $familiar->parentescos_id = $asociadosFamiliar->parentescos_id;
                    $familiar->familiares_tipo_id = $asociadosFamiliar->familiares_tipo_id;
                    $familiar->contrato_id = $contrato->id;
                    $familiar->periodo = $this->periodoActivo;
                    $familiar->save();
                }
            }
        }
        //generame el codigo para actualizar el paso del contrato al verificar que se creo el contrato

        if ($contrato) {
            DB::table('servicio_funerario_contrato_pasos')
                ->updateOrInsert(
                    ['asociado_idasociado' => Auth::user()->asociado_idasociado],
                    ['paso' => 5]
                );

            // $this->alert('success', 'Contrato generado.', [
            //     'position' =>  'center',
            //     'timer' =>  '3000',
            //     'toast' =>  false,
            //     'text' =>  '',
            //     'confirmButtonText' =>  'Aceptar',
            //     'cancelButtonText' =>  'Cancelar',
            //     'showCancelButton' =>  true,
            //     'showConfirmButton' =>  true,
            //     'listener' => 'confirmed',
            //     'onConfirmed' => 'confirmed',
            // ]);
            session()->flash('message', 'Actualizado exitosamente.');
            return $this->redirect('/dashboard/servicio-funerario/contratos');
        }
    }

    public function generarContratoCAPROF_UNELLEZ()
    {

        // DB::table('servicio_funerario_contratos')->where('asociado_idasociado', auth()->user()->asociado_idasociado)
        //     ->where('periodo', $this->periodoActivo)->delete();

        //verifico si exite un contrato previo
        $contrato = Contrato::where('asociado_idasociado', auth()->user()->asociado_idasociado)->where('periodo', $this->periodoActivo);
        //actualizo el contrato
        if ($contrato->exists()) {
            $contrato->update([
                'numero_cb' => $this->totales['carga_basica'],
                'monto_cb' => $this->totales['aporte_asociado'] + $this->totales['aporte_familiares_basico'],
                'numero_ca' => $this->totales['carga_adicional'],
                'monto_ca' => $this->totales['familiares_adicionales_menores'] + $this->totales['familiares_adicionales_mayores'],
                'numero_cn' => 12,
                'monto_cn' => $this->totales['total'] / 12,
                'numero_cesp' => 0,
                'monto_cesp' => 0.00,
                'monto_total_contrato' => $this->totales['total'],
                'estatus' => 1,
                'fecha' => date('Y-m-d'),
                'usuario' => auth()->user()->id,
                'anulado_por' => null,
                'fecha_anulacion' => null,
            ]);
        } else {
            //si no exite lo creo nuevo el contrato
            $contrato = new Contrato();
            $contrato->asociado_idasociado  = auth()->user()->asociado_idasociado;
            $contrato->cedula_soc           = auth()->user()->asociado->cedula_soc;
            $contrato->periodo              = $this->periodoActivo;
            $contrato->numero_cb            = $this->totales['carga_basica'];
            $contrato->monto_cb             =  $this->totales['aporte_asociado'] + $this->totales['aporte_familiares_basico'];
            $contrato->numero_ca            = $this->totales['carga_adicional'];
            $contrato->monto_ca             = $this->totales['familiares_adicionales_menores'] + $this->totales['familiares_adicionales_mayores'];
            $contrato->numero_cn            = 12;
            $contrato->monto_cn             = $this->totales['total'] / 12;
            $contrato->numero_cesp          = 0;
            $contrato->monto_cesp           = 0.00;
            $contrato->monto_total_contrato = $this->totales['total'];
            $contrato->estatus              = 1;
            $contrato->fecha = date('Y-m-d');
            $contrato->usuario = auth()->user()->id;
            $contrato->save();
        }
        //generame el codigo para actualizar el paso del contrato al verificar que se creo el contrato

        if ($contrato) {
            DB::table('servicio_funerario_contrato_pasos')
                ->updateOrInsert(
                    ['asociado_idasociado' => Auth::user()->asociado_idasociado],
                    ['paso' => 5]
                );

            // $this->alert('success', 'Contrato generado.', [
            //     'position' =>  'center',
            //     'timer' =>  '3000',
            //     'toast' =>  false,
            //     'text' =>  '',
            //     'confirmButtonText' =>  'Aceptar',
            //     'cancelButtonText' =>  'Cancelar',
            //     'showCancelButton' =>  true,
            //     'showConfirmButton' =>  true,
            //     'listener' => 'confirmed',
            //     'onConfirmed' => 'confirmed',
            // ]);
            session()->flash('message', 'Actualizado exitosamente.');
            return $this->redirect('/dashboard/servicio-funerario/contratos');
        }
    }

    public function confirmed()
    {
        $this->confirmGenerarContrato = false;
    }
    public function caprofFormula()
    {
        // Recuperar la formula de la base de datos
        $formula = DB::table('servicio_funerario_formulas')->where('nombre', 'CAPROF_UNELLEZ')->value('formula');

        // Recuperar el valor de las variables de la tabla de variables
        $variables = DB::table('servicio_funerario_formulas_variables')->pluck('valor', 'variable')->toArray();

        //extraigo y creo las variables de la base de datos
        extract($variables);

        //obtengo el numero de miembros del grupo familiar basico

        $carga_basica = AsociadosFamiliares::where('asociado_idasociado', auth()->user()->asociado->idasociado)
            ->where('familiares_tipo_id', 1)
            ->count();

        //obtengo el numero de miembros delA  grupo familiar adicional
        $carga_adicional = AsociadosFamiliares::where('asociado_idasociado', auth()->user()->asociado->idasociado)->with('familiares')
            ->where('familiares_tipo_id', 2)
            ->get();

        $familiares_adicionales = array();

        foreach ($carga_adicional as $adicional) {
            $familiares_adicionales[] = array('edad' => age($adicional->familiares->fecha_nac));
        }

        // Calcular los aportes correspondientes
        foreach ($familiares_adicionales as $familiar) {

            if ($familiar['edad'] <= $edad) {

                $aporte_familiares_adicionales_menores += $porcentaje_aporte_familiares_adicionales_menores * $sueldo_base;
                // $aporte_familiares_adicionales_menores += $aporte_familiar;
                $familiares_adicionales_menores++;
            } elseif ($familiar['edad'] > $edad) {

                $aporte_familiares_adicionales_mayores += $porcentaje_aporte_familiares_adicionales_mayores * $sueldo_base;
                //$aporte_familiares_adicionales_mayores += $aporte_familiar;
                $familiares_adicionales_mayores++;
            }
        }

        //calcular el aporte del asociado
        $aporte_asociado = $porcentaje_aporte_asociado * $sueldo_base;

        //calcular el aporte del grupo familiar basico
        $aporte_familiares_basico = $porcentaje_aporte_familiares_basico * $sueldo_base * $carga_basica;
        /*  echo "Aporte asociado: $aporte_asociado\n<br>";
        echo "Carga Basica: $carga_basica\n<br>";
        echo "Carga Adicional:  ".count($carga_adicional)."\n<br>";
        echo "Aporte de familiares basico: $aporte_familiares_basico\n<br>";
        echo "Aporte de familiares adicionales menores: $aporte_familiares_adicionales_menores\n<br>";
        echo "Aporte de familiares adicionales mayores: $aporte_familiares_adicionales_mayores\n<br>";
        echo "El numero de familiares menores es: $familiares_adicionales_menores\n<br>";
        echo "El numero de familiares mayores es: $familiares_adicionales_mayores\n<br>";
*/
        // Reemplazar las variables en la fórmula con sus valores correspondientes
        foreach ($variables as $nombre => $valor) {
            if (!isset($$nombre)) { // Verificar si la variable ya tiene un valor asignado
                $$nombre = $valor;  // Asignar el valor de la base de datos si la variable no tiene un valor asignado
            }
            $formula = str_replace("{{$nombre}}", $$nombre, $formula);
        }
        // ({aporte_asociado} * {sueldo_base}) + ({aporte_grupo_familiar} * {sueldo_base} * {carga_basica}  + {aporte_familiares_adicionales_menores} + {aporte_familiares_adicionales_mayores})

        // Mostrar el resultado
        $resultado = eval("return $formula;");
        //echo $formula . "\n<br>";
        //echo $resultado;
        //arreglo de variables
        $this->totales = array(
            'edad' => $edad,
            'sueldo_base' => $sueldo_base,
            'aporte_asociado' => $aporte_asociado,
            'carga_basica' => $carga_basica,
            'carga_adicional' => count($carga_adicional),
            'aporte_familiares_basico' => $aporte_familiares_basico,
            'familiares_adicionales_menores'  => $familiares_adicionales_menores,
            'familiares_adicionales_mayores' => $familiares_adicionales_mayores,

            'aporte_familiares_adicionales_menores'  => $aporte_familiares_adicionales_menores,
            'aporte_familiares_adicionales_mayores' => $aporte_familiares_adicionales_mayores,
            'total' => $resultado
        );
    }

    public function contratoMasivos()
    {
        //obtengo el numero de miembros del grupo familiar basico y adicional
        $this->caypuezFormula();

        //obtengo las cedulas de las personas que pueden contratar
        $cedulas = DB::table('servicio_funerario_contratos_masivos')
            ->where('estatus', 0)
            ->pluck('cedula')
            ->toArray();

        $fechaActual = Carbon::now()->format('Y-m-d');
        //obtengo el periodo activo
        $this->periodoActivo = DB::table('apertura_procesos')->whereDate('fecha_desde', '<=', $fechaActual)
            ->whereDate('fecha_hasta', '>=', $fechaActual)
            ->where('codigo_v', 'SF')
            ->first()->periodo;

        //recorro cada una de las cedulas y actualizo o creo el contrato
        foreach ($cedulas as $cedula) {

            //obtengo los datos del asociado
            $asociado = Asociado::where('cedula_soc', $cedula)
                ->first();

            $contrato = Contrato::where('cedula_soc', $cedula)->where('periodo', $this->periodoActivo)->first();

            //actualizo el contrato
            if ($contrato) {

                $contrato->update([
                    'numero_cb' => $this->totales['cargaBasica'],
                    'monto_cb' => $this->totales['precioCargaBasica'],
                    'numero_ca' => $this->totales['cargaAdicional'],
                    'monto_ca' => $this->totales['totalCargaAdicional'],
                    'numero_cn' => $this->totales['numeroCn'],
                    'monto_cn' => $this->totales['totalCargaAdicional'] / $this->totales['numeroCn'],
                    'numero_cesp' => $this->totales['numeroCesp'],
                    'monto_cesp' => $this->totales['precioCargaBasica'] / $this->totales['numeroCesp'],
                    'monto_total_contrato' => $this->totales['total'],
                    'estatus' => 1,
                    'fecha' => date('Y-m-d'),
                    'usuario' => auth()->user()->id,
                    'anulado_por' => null,
                    'fecha_anulacion' => null,

                ]);
            } else {
                //si no exite lo creo nuevo el contrato
                $contrato = new Contrato();
                $contrato->asociado_idasociado  = $asociado->idasociado;
                $contrato->cedula_soc           = $asociado->cedula_soc;
                $contrato->periodo              = $this->periodoActivo;
                $contrato->numero_cb            = $this->totales['cargaBasica'];
                $contrato->monto_cb             = $this->totales['precioCargaBasica'];
                $contrato->numero_ca            = $this->totales['cargaAdicional'];
                $contrato->monto_ca             = $this->totales['totalCargaAdicional'];
                $contrato->numero_cn            = $this->totales['numeroCn'];
                $contrato->monto_cn             = $this->totales['totalCargaAdicional'] / $this->totales['numeroCn'];
                $contrato->numero_cesp          = $this->totales['numeroCesp'];
                $contrato->monto_cesp           = $this->totales['precioCargaBasica'] / $this->totales['numeroCesp'];
                $contrato->monto_total_contrato = $this->totales['total'];
                $contrato->estatus              = 1;
                $contrato->fecha = date('Y-m-d');
                $contrato->usuario = auth()->user()->id;
                $contrato->save();

                //ahora copio el grupo familiar y lo registro con el nuevo id de contrato y periodo

                $asociadosFamiliares = AsociadosFamiliares::where('asociado_idasociado', $asociado->idasociado)
                    ->where('periodo', $this->periodoActivo - 1)
                    ->get();

                if ($asociadosFamiliares->count() > 0) {
                    foreach ($asociadosFamiliares as $asociadosFamiliar) {
                        $familiar = new AsociadosFamiliares();
                        $familiar->asociado_idasociado = $asociadosFamiliar->asociado_idasociado;
                        $familiar->familiares_id = $asociadosFamiliar->familiares_id;
                        $familiar->parentescos_id = $asociadosFamiliar->parentescos_id;
                        $familiar->familiares_tipo_id = $asociadosFamiliar->familiares_tipo_id;
                        $familiar->contrato_id = $contrato->id;
                        $familiar->periodo = $this->periodoActivo;
                        $familiar->save();
                    }
                }
            }



            //generame el codigo para actualizar el paso del contrato al verificar que se creo el contrato

            if ($contrato) {
                DB::table('servicio_funerario_contrato_pasos')
                    ->updateOrInsert(
                        ['asociado_idasociado' => Auth::user()->asociado_idasociado],
                        ['paso' => 5]
                    );


                DB::table('servicio_funerario_contratos_masivos')
                    ->where('cedula', $contrato->cedula_soc)
                    ->update(['estatus' => 1]);
            }
        }
    }

    public function contratoMasivo()
    {
        $cedulas = DB::table('asociados_contratos_masivo')
            ->whereNotIn('cedula', function ($query) {
                $query->select('cedula_soc')->from('servicio_funerario_contratos');
            })
            ->where('estatus', '0')
            ->get();

        foreach ($cedulas as $cedula) {
            //obtengo el periodo activo
            $periodo = '2025';

            // Recuperar la formula de la base de datos
            $formula = DB::table('servicio_funerario_formulas')->where('nombre', 'CAPROF_UNELLEZ')->value('formula');

            // Recuperar el valor de las variables de la tabla de variables
            $variables = DB::table('servicio_funerario_formulas_variables')->pluck('valor', 'variable')->toArray();

            //extraigo y creo las variables de la base de datos
            extract($variables);

            //obtengo los datos del asociado
            $asociado = Asociado::where('cedula_soc', $cedula->cedula)
                ->first();

            //obtengo el numero de miembros del grupo familiar basico

            $carga_basica = AsociadosFamiliares::where('asociado_idasociado', $asociado->idasociado)
                ->where('familiares_tipo_id', 1)
                ->count();

            //obtengo el numero de miembros delA  grupo familiar adicional
            $carga_adicional = AsociadosFamiliares::where('asociado_idasociado', $asociado->idasociado)->with('familiares')
                ->where('familiares_tipo_id', 2)
                ->get();

            $familiares_adicionales = array();

            foreach ($carga_adicional as $adicional) {
                $familiares_adicionales[] = array('edad' => age($adicional->familiares->fecha_nac));
            }

            // Calcular los aportes correspondientes
            foreach ($familiares_adicionales as $familiar) {

                if ($familiar['edad'] <= $edad) {

                    $aporte_familiares_adicionales_menores += $porcentaje_aporte_familiares_adicionales_menores * $sueldo_base;
                    // $aporte_familiares_adicionales_menores += $aporte_familiar;
                    $familiares_adicionales_menores++;
                } elseif ($familiar['edad'] > $edad) {

                    $aporte_familiares_adicionales_mayores += $porcentaje_aporte_familiares_adicionales_mayores * $sueldo_base;
                    //$aporte_familiares_adicionales_mayores += $aporte_familiar;
                    $familiares_adicionales_mayores++;
                }
            }

            //calcular el aporte del asociado
            $aporte_asociado = $porcentaje_aporte_asociado * $sueldo_base;

            //calcular el aporte del grupo familiar basico
            $aporte_familiares_basico = $porcentaje_aporte_familiares_basico * $sueldo_base * $carga_basica;
            echo "Aporte asociado: $aporte_asociado\n<br>";
            echo "Carga Basica: $carga_basica\n<br>";
            echo "Carga Adicional:  " . count($carga_adicional) . "\n<br>";
            echo "Aporte de familiares basico: $aporte_familiares_basico\n<br>";
            echo "Aporte de familiares adicionales menores: $aporte_familiares_adicionales_menores\n<br>";
            echo "Aporte de familiares adicionales mayores: $aporte_familiares_adicionales_mayores\n<br>";
            echo "El numero de familiares menores es: $familiares_adicionales_menores\n<br>";
            echo "El numero de familiares mayores es: $familiares_adicionales_mayores\n<br>";

            // Reemplazar las variables en la fórmula con sus valores correspondientes
            foreach ($variables as $nombre => $valor) {
                if (!isset($$nombre)) { // Verificar si la variable ya tiene un valor asignado
                    $$nombre = $valor;  // Asignar el valor de la base de datos si la variable no tiene un valor asignado
                }
                $formula = str_replace("{{$nombre}}", $$nombre, $formula);
            }
            // ({aporte_asociado} * {sueldo_base}) + ({aporte_grupo_familiar} * {sueldo_base} * {carga_basica}  + {aporte_familiares_adicionales_menores} + {aporte_familiares_adicionales_mayores})

            // Mostrar el resultado
            $resultado = eval("return $formula;");
            //echo $formula . "\n<br>";
            //echo $resultado;
            //arreglo de variables
            $this->totales = array(
                'edad' => $edad,
                'sueldo_base' => $sueldo_base,
                'aporte_asociado' => $aporte_asociado,
                'carga_basica' => $carga_basica,
                'carga_adicional' => count($carga_adicional),
                'aporte_familiares_basico' => $aporte_familiares_basico,
                'familiares_adicionales_menores'  => $familiares_adicionales_menores,
                'familiares_adicionales_mayores' => $familiares_adicionales_mayores,

                'aporte_familiares_adicionales_menores'  => $aporte_familiares_adicionales_menores,
                'aporte_familiares_adicionales_mayores' => $aporte_familiares_adicionales_mayores,
                'total' => $resultado

            );


            //configuracion de contrato
            $contrato = new Contrato();
            $contrato->asociado_idasociado  = $asociado->idasociado;
            $contrato->cedula_soc           = $asociado->cedula_soc;
            $contrato->periodo              = $periodo;
            $contrato->numero_cb            = $this->totales['carga_basica'];
            $contrato->monto_cb             = $this->totales['aporte_asociado'] + $this->totales['aporte_familiares_basico'];
            $contrato->numero_ca            = $this->totales['carga_adicional'];
            $contrato->monto_ca             = $this->totales['aporte_familiares_adicionales_menores'] + $this->totales['aporte_familiares_adicionales_mayores'];
            $contrato->numero_cn            = 12;
            $contrato->monto_cn             = $this->totales['total'] / 12;
            $contrato->numero_cesp          = 0;
            $contrato->monto_cesp           = 0.00;
            $contrato->monto_total_contrato = $this->totales['total'];
            $contrato->estatus              = 1;
            $contrato->fecha = date('Y-m-d');
            $contrato->usuario = $asociado->idasociado;
            $contrato->save();


            $aporte_asociado = 0;
            $aporte_familiares_basico = 0;
            $aporte_familiares_adicionales_menores = 0;
            $aporte_familiares_adicionales_mayores = 0;
            $carga_basica = 0;
            $carga_adicional = 0;
            $familiares_adicionales_menores = 0;
            $familiares_adicionales_mayores = 0;
            $this->totales = 0;
            $familiares_adicionales = 0;
            $this->totales = 0;
            $resultado = 0;

            DB::table('asociados_contratos_masivo')
                ->where('cedula', $cedula->cedula)
                ->update(['estatus' => 1]);
        }
        // return redirect()->to('/inscripcion-servicio');
    }


    public function render()
    {
        $app_name = config('app.name');
        return view("frontend::livewire.servicio-funerario.contratos.components.form.form-detalles-contrato-$app_name");
    }
}
