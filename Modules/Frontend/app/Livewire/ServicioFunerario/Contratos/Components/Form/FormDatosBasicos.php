<?php

namespace Modules\Frontend\Livewire\ServicioFunerario\Contratos\Components\Form;

use Carbon\Carbon;
use App\Models\Genero;
use Livewire\Component;
use App\Models\Asociado;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Lazy;

#[Lazy]
class FormDatosBasicos extends Component
{
    public Asociado $asociado;
    public $sexo;
    public $fecha_nac;
    public $estado_civil;
    public $email;
    public $direccion;
    public $numero_telefpers;
    public $numero_telefofi;
    public $numero_telefcont;


    //atributos
    public $vicerrectorados;
    public $estadosCiviles;
    public $generos;

    protected function rules()
    {
        return [
            'sexo' => 'required',
            'fecha_nac' => 'required',
            'email' => 'required',
            'direccion' => 'required',
        ];
    }

    protected $messages =  [
        'sexo.required' => 'El sexo es requerido',
        'fecha_nac.required' => 'La fecha de nacimiento es requerida',
        'email.required' => 'El email es requerido',
        'direccion.required' => 'La dirección es requerida',
    ];

    public function mount()
    {
        $this->sexo = $this->asociado->sexo;
        $this->fecha_nac = $this->asociado->fecha_nac;
        $this->estado_civil = $this->asociado->estado_civil;
        $this->email = $this->asociado->email;
        $this->direccion = $this->asociado->direccion;
        $this->numero_telefpers = $this->asociado->numero_telefpers;
        $this->numero_telefofi = $this->asociado->numero_telefofi;
        $this->numero_telefcont = $this->asociado->numero_telefcont;
    }

    public function render()
    {
        //generos
        $this->generos = Genero::all();
        //estados civiles
        $this->estadosCiviles =  DB::table('codigos_varios')->where('ref_v', 'AA')->get();

        return view('frontend::livewire.servicio-funerario.contratos.components.form.form-datos-basicos');
    }

    public function save()
    {
        $this->validate();
        Asociado::find($this->asociado->idasociado)
            ->update([
                'estado_civil' => $this->estado_civil,
                'sexo' => $this->sexo,
                'fecha_nac' => $this->fecha_nac,
                'email' => $this->email,
                'direccion' => $this->direccion,
                'numero_telefpers' => $this->numero_telefpers,
                'numero_telefofi' => $this->numero_telefofi,
                'numero_telefcont' => $this->numero_telefcont
            ]);
        DB::table('servicio_funerario_contrato_pasos')
            ->updateOrInsert(
                ['asociado_idasociado' => Auth::user()->asociado_idasociado],
                ['paso' => 2]
            );

        session()->flash('message', 'Actualizado exitosamente.');
        return $this->redirect('/dashboard/servicio-funerario/contratos');
    }
}
