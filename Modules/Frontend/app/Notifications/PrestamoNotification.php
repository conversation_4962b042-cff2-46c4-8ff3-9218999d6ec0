<?php

namespace Modules\Frontend\Notifications;

use App\Models\AsociadosSolicitudesPrestamos;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class PrestamoNotification extends Notification
{
    use Queueable;
    public $solicitud;
    /**
     * Create a new notification instance.
     */
    public function __construct(AsociadosSolicitudesPrestamos $solicitud)
    {
        $this->solicitud = $solicitud;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', 'https://laravel.com')
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'title' => 'Solicitud de prestamo #' . $this->solicitud->idsolicitud . ' creada por ' . auth()->user()->asociado->nombres,
            'asociado' => auth()->user()->asociado->nombres,
            'solicitud_id' => $this->solicitud->idsolicitud,
            'fecha' => $this->solicitud->fecha_sol,
            'route' => route('admin.solicitudes.prestamos.show', $this->solicitud),
        ];
    }
}
