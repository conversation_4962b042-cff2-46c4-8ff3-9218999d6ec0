<?php

namespace Modules\Frontend\Listeners;

use App\Models\User;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Notification;
use Modules\Frontend\Notifications\PrestamoNotification;

class SolicitudPrestamoNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        User::whereHas('roles', function ($user) {
            $user->where('name', '=', 'admin');
        })
            ->where('id', '!=', auth()->user()->id)
            ->get()
            ->each(function (User $user) use ($event) {
                Notification::send($user, new PrestamoNotification($event->solicitud));
            });
    }
}
