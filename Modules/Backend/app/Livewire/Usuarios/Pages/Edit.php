<?php

namespace Modules\Backend\Livewire\Usuarios\Pages;

use App\Models\User;
use Livewire\Component;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;


class Edit extends Component
{

    use LivewireAlert;
    const PERMISSIONS = [
        'edit' => 'admin.users.edit',
    ];

    public $editUsuarioModal = false;
    public User $user;
    public $username;
    public $email;
    public $password;
    public $password_confirmation;

    protected $listeners = [
        'editUsuario' => 'edit',
        'saveUsuario' => 'save',
    ];
    public function rules()
    {
        return [
            'username' => 'required|min:3|max:20|unique:users,username,' . $this->user->id,
            'password' => 'nullable|min:8|confirmed',
        ];
    }

    public function messages()
    {
        return [
            'username.required' => 'El nombre de usuario es requerido',
            'username.min' => 'El nombre de usuario debe tener al menos 3 caracteres',
            'username.max' => 'El nombre de usuario debe tener máximo 20 caracteres',
            'username.unique' => 'El nombre de usuario ya existe',
            'password.min' => 'La contraseña debe tener al menos 8 caracteres',
            'password.confirmed' => 'Las contraseñas no coinciden',
        ];
    }

    public function render()
    {
        return view('backend::livewire.usuarios.pages.edit');
    }
    public function edit()
    {
        $this->editUsuarioModal = true;
        $this->username = $this->user->username;
    }


    public function mount()
    {
        $this->username = $this->user->username;
        $this->email = $this->user->email;
    }

    public function save()
    {
        $this->validate();
        $this->user->username = $this->username;
        if ($this->password) {
            $this->user->password = bcrypt($this->password);
        }
        $this->user->save();

        session()->flash('message', 'Actualizado exitosamente.');
        $this->alert('success', 'Procesado exitosamente.', [
            'position' =>  'center',
            'timer' =>  '3000',
            'toast' =>  false,
            'text' =>  '',
            'confirmButtonText' =>  'Aceptar',
            'cancelButtonText' =>  'Cancelar',
            'showCancelButton' =>  true,
            'showConfirmButton' =>  true,
            'listener' => 'confirmed',
            'onConfirmed' => 'confirmed',
        ]);
    }
}
