<?php

namespace Modules\Backend\Livewire\ServicioFunerario\Contratos\Pages;

use App\Models\Contrato;
use Livewire\Component;
use Illuminate\Support\Facades\Http;
use Jantinnerezo\LivewireAlert\LivewireAlert;

class Index extends Component
{
    use LivewireAlert;

    const PERMISSIONS = [
        'index' => 'admin.servicio-funerario.contratos.index',
    ];

    public function render()
    {
        $total_contratos = Contrato::count();
        $total_contratos_activos = Contrato::where('estatus', 1)->count();
        $total_contratos_anulados = Contrato::where('estatus', 2)->count();

        return view('backend::livewire.servicio-funerario.contratos.pages.index', [
            'total_contratos' => $total_contratos,
            'total_contratos_activos' => $total_contratos_activos,
            'total_contratos_anulados' => $total_contratos_anulados,
        ]);
    }

    public function procesarContratosMasivos()
    {
        $this->dispatch('actualizarContratosMasivos');
    }
}
