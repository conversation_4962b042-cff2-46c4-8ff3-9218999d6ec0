<?php

namespace Modules\Backend\Livewire\Asociados\Components\Form;

use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Lazy;
use Livewire\Component;

#[Lazy]
class FormDatosLaboral extends Component
{
    //atributos
    public $asociado;
    public $tipoPersonal;
    public $vicerrectorados;
    public $tipo_pers;
    public $codigo_dep;

    protected function rules()
    {
        return [
            'tipo_pers' => 'required',
            'codigo_dep' => 'required',
        ];
    }

    protected $messages = [
        'tipo_pers.required' => 'El tipo de personal es requerido',
        'codigo_dep.required' => 'El vicerrectorado es requerido',
    ];

    public function mount()
    {
        $this->tipo_pers = $this->asociado->tipo_pers;
        $this->codigo_dep = $this->asociado->codigo_dep;
    }

    public function render()
    {
        //tipo de personal
        $this->tipoPersonal = DB::table('codigos_varios')->where('ref_v', 'P')->get();
        //vicerrectorado
        $this->vicerrectorados = DB::table('codigos_varios')->where('ref_v', 'D')->get();
        return view('backend::livewire.asociados.components.form.form-datos-laboral');
    }

    public function save()
    {
        $this->validate();
        $this->asociado->tipo_pers = $this->tipo_pers;
        $this->asociado->codigo_dep = $this->codigo_dep;
        $this->asociado->update();
        session()->flash('message', 'Actualizado exitosamente.');
    }
}
