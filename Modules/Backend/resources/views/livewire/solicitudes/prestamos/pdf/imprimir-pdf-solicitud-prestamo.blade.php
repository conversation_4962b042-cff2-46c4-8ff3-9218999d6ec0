<div>


    <style>
        body {
            margin: 0 auto;
            font-size: 8pt;
            font-family: Arial, sans-serif;
        }

        hr {
            border: 0;
            height: 0.2px;
            background-color: #757575;
            margin-top: 10px;
            margin-bottom: 10px;
        }

        table {
            width: 100%;
        }

        table,
        th,
        td {
            /* border: 1px solid rgb(231, 231, 231); */
            border-collapse: collapse;
        }
    </style>




    <span
        style="display: inline-block; margin: 0.00px 0.00px; border: 0.00px solid #000000; transform: rotate(0.00rad) translateZ(0px); -webkit-transform: rotate(0.00rad) translateZ(0px); width: 136.96px; height: 60.00px;"><img
            alt="" src="{{ asset('assets/img/logo/' . trim($empresa->siglas_caja) . '.png') }}"
            style="height: 60.00px; margin-left: 0.00px; margin-top: 0.00px; transform: rotate(0.00rad) translateZ(0px); -webkit-transform: rotate(0.00rad) translateZ(0px);"
            title="">
    </span>

    <p>
        {{ $empresa->nombre_caja }}<br>
        {{ $empresa->sector_publico }}<br>
        Rif:{{ $empresa->numero_rif }}
    </p>

    <!-- Header -->

    <header>
        <h1>Planilla de solicitud</h1>
    </header>
    <table>
        <thead>
            <tr>
                <td colspan="4">
                    <h3> <b>Datos del asociado::</b></h3>
                </td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><b>Nombres y Apellidos::</b> </td>
                <td>{{ $solicitud->asociado->nombres }} {{ $solicitud->asociado->apellidos }}</td>
            </tr>
            <tr>
                <td><b>C.I:</b></td>
                <td>{{ $solicitud->asociado->cedula_soc }}</td>
                <td><b>Condición:</b></td>
                <td> {{ $solicitud->asociado->condicion->descripcion }}</td>
            </tr>
            <tr>
                <td><b>Fecha ingreso: </b></td>
                <td>{{ date('d/m/Y', strtotime($solicitud->asociado->fecha_ingreso)) }} </td>

                <td><b>N° Expediente:</b></td>
                <td>{{ $solicitud->asociado->numero_exp }}</td></b>
            </tr>
            <tr>

                <td><b>Tipo personal:</b></td>
                <td>{{ $solicitud->asociado->tipoPersonal->descri_v ?? '' }}</td>
                <td><b>Teléfono personal:</b></td>
                <td> {{ $solicitud->asociado->numero_telefpers }}</td>
            </tr>

        </tbody>
    </table>
    <!-- Main Content -->

    <hr>

    <table>

        <thead>
            <tr>
                <td colspan="4">
                    <h3>Detalles de Ahorros::</h3>
                </td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td></td>
                <td><b>Acreditados::</b></td>
                <td><b>Pendientes::</b></td>
                <td><b>Haberes Netos Disponibles::</b></td>
            </tr>
            <tr>
                <td><b>Personales::</b></td>
                <td>{{ number_format($solicitud->asociado->saldah_persac, 2) }}</td>
                <td>{{ number_format($solicitud->asociado->saldah_perspe, 2) }}</td>
                <td><b>80 % sobre total de
                        haberes:
                    </b>
                </td>
            </tr>
            <tr>
                <td><b>Patronales::</b></td>
                <td>{{ number_format($solicitud->asociado->saldah_patrac, 2) }}</td>
                <td>{{ number_format($solicitud->asociado->saldah_patrpe, 2) }}</td>
                <td>
                    {{ number_format(($solicitud->asociado->saldah_persac + $solicitud->asociado->saldah_patrac + $solicitud->asociado->saldah_voluac) * 0.8, 2) }}
                </td>
            </tr>
            <tr>
                <td><b>Voluntarios::</b></td>
                <td>{{ number_format($solicitud->asociado->saldah_voluac, 2) }}</td>
                <td>{{ number_format($solicitud->asociado->saldah_volupe, 2) }}</td>
                <td> <b>20 %
                        Bloqueo
                        Estatutario:</b>
                </td>
            </tr>
            <tr>
                <td><b>Total (100%)</b>...</td>
                <td>{{ number_format($solicitud->asociado->saldah_persac + $solicitud->asociado->saldah_patrac + $solicitud->asociado->saldah_voluac, 2) }}
                </td>
                <td>
                    {{ number_format($solicitud->asociado->saldah_perspe + $solicitud->asociado->saldah_patrpe + $solicitud->asociado->saldah_volupe, 2) }}
                </td>
                <td> {{ number_format(($solicitud->asociado->saldah_persac + $solicitud->asociado->saldah_patrac + $solicitud->asociado->saldah_voluac) * 0.2, 2) }}
                </td>
            </tr>
            <tr>
                <td></td>
                <td colspan="2"><b>Total de Haberes (Acreditado + Pendiente.) ::</b>
                    {{ number_format($solicitud->asociado->saldah_persac + $solicitud->asociado->saldah_patrac + $solicitud->asociado->saldah_voluac + $solicitud->asociado->saldah_perspe + $solicitud->asociado->saldah_patrpe + $solicitud->asociado->saldah_volupe, 2) }}
                </td>
                <td></td>
            </tr>
        </tbody>
    </table>
    <hr>
    <!-- Passenger Details -->
    <h3>Detalles de la solicitud::</h3>

    <table style="border: 1px solid rgb(231, 231, 231);">
        <thead>
            <tr>
                <td style="width: 30%;"><b>LÍNEA DE PRÉSTAMO</b></td>
                <td><b>FECHA SOLICITUD</b></td>
                <td><b>MONTO</b></td>
                <td><b>CUOTAS</b></td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>({{ $solicitud->prestamo->codigo_prest }}) {{ $solicitud->prestamo->descripcion }}</td>
                <td>{{ $solicitud->fecha_sol }}</td>
                <td>{{ $solicitud->montot_prest }}</td>
                <td>{{ $solicitud->numero_cn }}</td>
            </tr>
        </tbody>
    </table>

    @if ($solicitud->motivo != 'Solicitud de Préstamo')
        <h3>Motivo de la solicitud::</h3>
        <p>{{ $solicitud->motivo }}</p>
    @endif

    <!-- Footer -->
    <footer>
        <hr>
        <p>{{ $empresa->direccion }}<br>Tlf. {{ $empresa->telefonos }}<br>
            Página web:{{ $empresa->sitio_web }}</p>
    </footer>
</div>
