<div class="card">
    <div class="card-body">
        <x-form submit="save">
            <x-slot name="title">
                {{ __('Asignacion de roles a ') }} {{ $user->name }}
            </x-slot>

            <x-slot name="description">
                {{ __('Asigne uno o mas roles a un usuario') }}
            </x-slot>
            <x-slot name="form">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            @if (session()->has('message'))
                                <div class="alert alert-success">{{ session('message') }}</div>
                            @endif
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Roles</th>
                                        <th>Asignado</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($roles as $role)
                                        <tr>
                                            <td>{{ $role->name }}</td>
                                            <td>
                                                <input type="checkbox" wire:model="userRoles"
                                                    value="{{ $role->name }}"
                                                    {{ in_array($role->name, $userRoles) ? 'checked' : '' }}>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </x-slot>

            <x-slot name="actions">
                <x-button class="mb-2 ml-2 btn btn-primary" wire:click="save"><i class="fas fa-save"></i>
                    {{ __('Guardar') }}
                </x-button>
            </x-slot>
        </x-form>
    </div>
</div>
