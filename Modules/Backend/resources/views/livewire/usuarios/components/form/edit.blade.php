<div>
    <div class="card">
        <div class="card-body">
            @if (session()->has('message'))
                <div class="alert alert-success"><i class="fas fa-info-circle"></i>
                    {{ session('message') }}</div>
            @endif
            <legend class="mt-2 mb-0 text-primary">Información del usuario</legend>
            <div class="col-md-12">
                <div class="form-row ">
                    <div class="col-md-12">
                        <div class="form-group">
                            <x-label for="username" value="{{ __('Usuario') }}" />
                            <x-input id="username" class="form-control" type="text" name="username"
                                wire:model="username" />
                            <x-input-error for="username" class="mt-2" />
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <x-label for="password" value="{{ __('Contraseña') }}" />
                            <x-input id="password" class="form-control" type="password" name="password"
                                wire:model="password" />
                            <x-input-error for="password" class="mt-2" />
                            <small class="form-text text-muted">Contraseña</small>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <x-label for="password_confirmation" value="{{ __('Repita Contraseña') }}" />
                            <x-input id="password_confirmation" class="form-control" type="password"
                                name="password_confirmation" wire:model="password_confirmation" />
                            <x-input-error for="password_confirmation" class="mt-2" />
                            <small class="form-text text-muted">Repita la contraseña</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <x-button class="mt-2 btn btn-primary" wire:click="save"><i class="fas fa-save"></i>
                {{ __('Guardar') }}
            </x-button>
        </div>
    </div>
</div>
