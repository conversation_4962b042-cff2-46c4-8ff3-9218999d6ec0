<div class="card">

    <div class="card-body">
        <div class="alert alert-warning" role="alert">
            <h4 class="alert-heading"> <i class="fas fa-info-circle"></i> Estimado asociado</h4>
            <p></p>
            <p class="mb-0">Este módulo está actualmente en proceso de revisión técnica debido a problemas de
                sincronización con el servidor web. Estamos trabajando diligentemente para resolver estos inconvenientes
                y garantizar que el módulo funcione correctamente. Agradecemos su paciencia y comprensión mientras
                abordamos esta situación. Si tiene alguna pregunta adicional, no dude en ponerse en contacto con
                nosotros. ¡Gracias por su colaboración! </p>
        </div>
        @if (session()->has('message'))
            <div class="alert alert-success"><i class="fas fa-info-circle"></i> {{ session('message') }}</div>
        @endif
        <div class="p-0 d-flex justify-content-between flex-xl-row flex-md-column flex-sm-row flex-column p-sm-3sss">
            <div class="mb-4 mb-xl-0">
                <div class="gap-2 mb-3 d-flex svg-illustration">
                    <span class="app-brand-logo demo">
                        <span
                            style="display: inline-block; margin: 0.00px 0.00px; border: 0.00px solid #000000; transform: rotate(0.00rad) translateZ(0px); -webkit-transform: rotate(0.00rad) translateZ(0px); width: 136.96px; height: 60.00px;"><img
                                alt="" src="{{ asset('assets/img/logo/' . config('app.name') . '.png') }}"
                                style="height: 60.00px; margin-left: 0.00px; margin-top: 0.00px; transform: rotate(0.00rad) translateZ(0px); -webkit-transform: rotate(0.00rad) translateZ(0px);"
                                title="">
                        </span>
                    </span>
                </div>
                <p class="mb-0">{{ $empresa->nombre_caja }}</p>
                <p class="mb-0">{{ $empresa->sector_publico }}</p>
                <p class="mb-0">Rif:{{ $empresa->numero_rif }}</p>
            </div>
            <div>
                <h4>Asociado #{{ $asociado->idasociado }}</h4>
                <div class="mb-0">
                    <span class="me-0">Actualizado al: </span>
                    <span class="fw-medium">{{ date('d/m/Y') }}</span>
                </div>
                <div>
                    <span class="me-0">Fecha de emisión:</span>
                    <span class="fw-medium">{{ date('d/m/Y') }}</span>
                </div>
            </div>
        </div>
        <!-- Header -->
        <hr>
        <header>
            <div class="row align-items-center">
                <div class="col-lg-12 text-sm-ensd">
                    <h2 class="mb-0">Estado de Cuenta: {{ $asociado->nombres }}
                        {{ $asociado->apellidos }}</h2>
                </div>
            </div>
        </header>
        <br>
        <!-- Main Content -->
        <main>
            <div class="table-responsive">
                <table class="table text-xs table-bordered table-sm table-striped">
                    <thead>
                        <tr>
                            <td colspan="4" class=""><span class="fw-600"><b>Detalles de Ahorros del
                                        Asociado</b></span>:
                                <span class="float-right text-center float-end justify-content-center"></span><b>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="fw-600 col-2"></td>
                            <td class="fw-600 col-4"><b>Acreditados::</b></td>
                            <td class="fw-600 col-2"><b>Pendientes::</b></td>
                            <td class="col-4"><b>Haberes Netos Disponibles::</b></td>
                        </tr>
                        <tr>
                            <td class="fw-600"><b>Personales::</b></td>
                            <td>{{ number_format($asociado->saldah_persac, 2) }}</td>
                            <td class="fw-600">{{ number_format($asociado->saldah_perspe, 2) }}</td>
                            <td><b>80 % sobre total de
                                    haberes:
                                </b>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-600"><b>Patronales::</b></td>
                            <td>{{ number_format($asociado->saldah_patrac, 2) }}</td>
                            <td class="fw-600">{{ number_format($asociado->saldah_patrpe, 2) }}</td>
                            <td>
                                @if (config('app.name') == 'CAPSTULA')
                                    {{ number_format(($asociado->saldah_persac + $asociado->saldah_patrac) * 0.8, 2) }}
                                @else
                                    {{ number_format(($asociado->saldah_persac + $asociado->saldah_patrac + $asociado->saldah_voluac) * 0.8, 2) }}
                                @endif
                                </b></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-600"><b>Voluntarios::</b></td>
                            <td>{{ number_format($asociado->saldah_voluac, 2) }}</td>
                            <td class="fw-600">{{ number_format($asociado->saldah_volupe, 2) }}</td>
                            <td> <b>20 %
                                    Bloqueo
                                    Estatutario:</b>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-600"><b>Total (100%)</b>...</td>
                            <td>
                                @if (config('app.name') == 'CAPSTULA')
                                    {{ number_format($asociado->saldah_persac + $asociado->saldah_patrac, 2) }}
                                @else
                                    {{ number_format($asociado->saldah_persac + $asociado->saldah_patrac + $asociado->saldah_voluac, 2) }}
                                @endif
                            </td>
                            <td class="fw-600">
                                @if (config('app.name') == 'CAPSTULA')
                                    {{ number_format($asociado->saldah_perspe + $asociado->saldah_patrpe, 2) }}
                                @else
                                    {{ number_format($asociado->saldah_perspe + $asociado->saldah_patrpe + $asociado->saldah_volupe, 2) }}
                                @endif
                            </td>
                            <td>
                                @if (config('app.name') == 'CAPSTULA')
                                    {{ number_format(($asociado->saldah_persac + $asociado->saldah_patrac) * 0.2, 2) }}
                                @else
                                    {{ number_format(($asociado->saldah_persac + $asociado->saldah_patrac + $asociado->saldah_voluac) * 0.8, 2) }}
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-600"></td>
                            <td colspan="2"><b>Total de Haberes (Acreditado + Pendiente.) ::</b>
                                {{ number_format($asociado->saldah_persac + $asociado->saldah_patrac + $asociado->saldah_voluac + $asociado->saldah_perspe + $asociado->saldah_patrpe + $asociado->saldah_volupe, 2) }}
                            </td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>


            @php
                $totalOrdinario = 0;
            @endphp
            @if ($prestamos->count() > 0)
                <!-- Passenger Details -->
                <h4 class="mt-2 text-4">Préstamos con Disponibilidad de Haberes</h4>
                <div class="table-responsive">
                    <table class="tables text-xs table-bordered text-1 table-sm" width="100%">
                        <thead>
                            <tr class="bg-light">
                                <th>LÍNEA DE PRÉSTAMO</th>
                                <th>FECHA</th>
                                <th>MONTO PRÉSTAMO</th>
                                <th>CUOTA<br>Normal / Especial</th>
                                <th>Nº CUOTAS<br>Normal / Especial</th>
                                <th>MONTO CUOTAS<br>Normal / Especial</th>
                                <th>CAPITAL PAGADO<br>Normal / Especial</th>
                                <th>SALDO DEUDOR<br>Normal / Especial</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($prestamos as $key => $prestamo)
                                <tr>
                                    <td><b>{{ $prestamo->idprestamo }}{{ $prestamo['controlDePrestamo']->descripcion }}
                                            (Bs.)
                                        </b></td>
                                    <td>{{ date('d/m/Y', strtotime($prestamo->fecha_prest)) }}</td>
                                    <td>{{ number_format($prestamo->montot_prest, 2) . 'Bs.' }}</td>
                                    <td> {{ number_format($prestamo->totalp_nom, 2) . 'Bs.' }} /
                                        {{ number_format($prestamo->montop_cesp, 2) . 'Bs.' }}</td>
                                    <td>{{ $prestamo->numero_cn }} / {{ $prestamo->numero_cesp }}</td>
                                    <td>{{ number_format($prestamo->montoc_nom, 2) . 'Bs.' }} /
                                        {{ number_format($prestamo->montop_cesp, 2) . 'Bs.' }}</td>
                                    <td>{{ $prestamo->salcap_nom < 0 ? number_format($prestamo->totalp_nom, 2) . 'Bs.' : number_format($prestamo->totalp_nom - $prestamo->salcap_nom, 2) . 'Bs.' }}
                                        /
                                        {{ number_format($prestamo->salcap_cesp, 2) . 'Bs.' }}</td>
                                    <td>{{ $prestamo->salcap_nom < 0 ? '0.00' : number_format($prestamo->salcap_nom, 2) . 'Bs.' }}
                                        /
                                        {{ $prestamo->salcap_cesp < 0 ? '0.00' : number_format($prestamo->salcap_cesp, 2) . 'Bs.' }}

                                    </td>
                                </tr>
                                @php
                                    //sumar totales de préstamos
                                    if ($prestamo->salcap_nom >= 0) {
                                        $totalOrdinario += $prestamo->salcap_nom;
                                    }
                                    if ($prestamo->salcap_cesp >= 0) {
                                        $totalOrdinario += $prestamo->salcap_cesp + $prestamo->interes_ce;
                                    }
                                @endphp
                            @endforeach
                            @php
                                $disp_financiera =
                                    ($asociado->saldah_persac + $asociado->saldah_patrac + $asociado->saldah_voluac) *
                                    0.8;
                                $disp_financiera_total = $disp_financiera - $totalOrdinario;
                            @endphp
                        </tbody>
                    </table>
                </div>
                <hr>
            @endif

            @php
                $totalExtOrdinarioBS = (float) 0;
                $totalExtOrdinarioUSD = (float) 0;
                $totalExtOrdinarioUSDCambio = (float) 0;
            @endphp

            @if ($financiamientos->count() > 0)
                <h4 class="mt-2 text-4">Financiamiento (Sin haberes)</h4>
                <div class="table-responsive">
                    <table class="tables text-xs table-bordered text-1" width="100%">
                        <thead>
                            <tr class="bg-light">
                                <th>LÍNEA DE FINANCIAMIENTO</th>
                                <th>FECHA</th>
                                <th>MONTO PRÉSTAMO<br>Bolivares / Ref.</th>
                                <th>MONTO TOTAL CUOTA<br>Normal / Especial</th>
                                <th>Nº CUOTAS<br>Normal / Especial</th>
                                <th>MONTO POR CUOTA<br>Normal / Especial</th>
                                <th>CAPITAL PAGADO<br>Normal / Especial</th>
                                <th>SALDO DEUDOR<br>Normal / Especial</th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach ($financiamientos as $key => $financiamiento)
                                <tr>
                                    <td><b>{{ $financiamiento['controlDePrestamo']->descripcion }}</b>
                                    </td>
                                    <td>{{ date('d/m/Y', strtotime($financiamiento->fecha_prest)) }}</td>
                                    <td>{{ number_format($financiamiento->montot_prest, 2) . 'Bs.' }} / (
                                        {{ number_format($financiamiento->montot_prest / $financiamiento->tasa_cambio, 2) . '$' }})
                                    </td>
                                    <td>{{ $financiamiento->condic_pagocn == 1 ? number_format($financiamiento->totalp_nom, 2) . 'Bs.' : number_format($financiamiento->montopnom_index, 2) . '$' }}
                                        /
                                        {{ $financiamiento->condic_pagoce == 1 ? number_format($financiamiento->montop_cesp, 2) . 'Bs.' : number_format($financiamiento->montopcesp_index, 2) . '$' }}
                                    </td>
                                    <td>{{ $financiamiento->numero_cn }} / {{ $financiamiento->numero_cesp }}</td>

                                    <td>
                                        {{ $financiamiento->condic_pagocn == 1 ? number_format($financiamiento->montoc_nom, 2) . 'Bs.' : number_format($financiamiento->montoc_nomindex, 2) . '$' }}
                                        /
                                        {{ $financiamiento->condic_pagoce == 1 ? number_format($financiamiento->monto_cesp, 2) . 'Bs.' : number_format($financiamiento->montocesp_index, 2) . '$' }}
                                    </td>
                                    <td>
                                        {{ $financiamiento->condic_pagocn == 1 ? number_format($financiamiento->totalp_nom - $financiamiento->salcap_nom, 2) . 'Bs.' : number_format($financiamiento->montopnom_index - $financiamiento->saldopnom_index, 2) . '$' }}
                                        /
                                        {{ $financiamiento->condic_pagoce == 1 ? number_format($financiamiento->totalp_nom - $financiamiento->salcap_nom, 2) . 'Bs.' : number_format($financiamiento->montopcesp_index - $financiamiento->salcap_cespindex, 2) . '$' }}
                                    </td>
                                    <td>
                                        {{ ($financiamiento->condic_pagocn == 1 ? ($financiamiento->salcap_nom < 0 ? '0.00' : number_format($financiamiento->salcap_nom, 2)) . 'Bs.' : $financiamiento->saldopnom_index < 0) ? '0.00' : number_format($financiamiento->saldopnom_index, 2) . '$' }}

                                        /
                                        {{ ($financiamiento->condic_pagoce == 1 ? ($financiamiento->salcap_cesp < 0 ? '0.00' : number_format($financiamiento->salcap_cesp, 2)) . 'Bs.' : $financiamiento->salcap_cespindex < 0) ? '0.00' : number_format($financiamiento->salcap_cespindex, 2) . '$' }}

                                        {{-- {{ number_format($financiamiento->prestamosRelacionDecuotasEspeciales->sum('saldo_cespindex'), 2) }} --}}
                                    </td>
                                    @php
                                        $totalExtOrdinarioBS += $financiamiento->salcap_nom;
                                    @endphp
                                </tr>
                            @endforeach
                            {{-- <tr>
                                <td colspan="5"></td>
                                <td><b>Total (BS):</b></td>
                                <td>{{ number_format($totalExtOrdinarioBS, 2) }}</td>
                            </tr>
                            <tr>
                                <td colspan="5"></td>
                                <td><b>Total (USD):</b></td>
                                <td>{{ number_format($totalExtOrdinarioUSD, 2) }}</td>
                            </tr> --}}
                        </tbody>
                    </table>
                </div>
            @endif

            <!-- Fare Details -->
            <h4 class="mt-2 text-4"></h4>
            <div class="table-responsive">
                <table class="table text-sm table-bordered table-sm">
                    <tbody>
                        <tr>
                            <td class="col-9 fw-500 text-end"><strong>Disponibilidad Financiera:</strong></td>
                            <td class="col-3 text-end">
                                <b>{{ isset($disp_financiera_total) ? number_format($disp_financiera_total = $disp_financiera_total, 2) : number_format($disp_financiera_total = ($asociado->saldah_persac + $asociado->saldah_patrac + $asociado->saldah_voluac) * 0.8, 2) }}</b>
                                Bs.
                            </td>
                        </tr>
                        {{-- <tr>
                            <td class="col-9 fw-500 text-end"><strong>Disponibilidad Real:</strong></td>
                            <td class="col-3 text-end">
                                @php
                                    $disp_real_total =
                                        $disp_financiera_total - ($totalExtOrdinarioBS + $totalExtOrdinarioUSDCambio);
                                @endphp
                                <b>{{ number_format($disp_real_total, 2) }} </b>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-9 fw-500 text-end"><strong>Porcentaje Excedente Sobre
                                    Disponibilidad Financiera:</strong></td>
                            <td class="col-3 text-end">
                                @php
                                    $porcentaje_sobregiro = 000;
                                    if ($disp_real_total != 0) {
                                        $porcentaje_sobregiro = ($disp_real_total / $disp_financiera_total) * 100;
                                        $porcentaje_sobregiro = $porcentaje_sobregiro - 100;
                                    }
                                @endphp
                                <b>{{ number_format($porcentaje_sobregiro, 2) }}%</b>

                            </td>
                        </tr> --}}
                        {{-- <tr>
                                <td class="col-9 fw-500 text-end"><strong>Total Amount:</strong></td>
                                <td class="col-3 text-end">$1195.00</td>
                            </tr> --}}
                    </tbody>
                </table>
            </div>
            <!-- Important Info -->
            {{-- <h4 class="mt-2 text-4">Important Instruction</h4>
                <ul class="text-1">
                    <li>One of the passengers in an e-ticket should carry proof of identification during the train
                        journey.
                    </li>
                    <li>The input for the proof of identity in case of cancellation/partial cancellation is also not
                        required now.</li>
                    <li>The passenger should also carry the Electronic Reservation Slip (ERS) during the train journey
                        failing which a penalty of $10 will be charged by the TTE/Conductor Guard.</li>
                </ul> --}}
        </main>
        <!-- Footer -->
        <footer class="text-center">
            <hr>
            <p>{{ $empresa->direccion }}<br>Tlf. {{ $empresa->telefonos }}<br>Página
                web:{{ $empresa->sitio_web }}</p>
            {{-- <hr> --}}
            <div class="btn-group-sm d-print-none">
                <button class="text-white bg-blue-700 btn btn-primary" wire:click="exportPdf"
                    wire:loading.attr="disabled">
                    <span wire:loading.remove><i class="fa fa-download"></i> Descargar</span>
                    <div wire:loading>
                        <i class="fa fa-download"></i> Generando estado de cuenta....
                    </div>
                </button>

                @can('admin.asociados.index')
                    <button class="text-white bg-blue-700 btn btn-primary" wire:click="sendEmail"
                        wire:loading.attr="disabled">
                        <span wire:loading.remove><i class="fas fa-inbox"></i> Enviar por correo</span>
                        <div wire:loading>
                            <i class="fas fa-inbox"></i> Enviando estado de cuenta....
                        </div>
                    </button>
                @endcan
            </div>
        </footer>
    </div>
</div>
