<div>
    @if ($contrato->estatus == 1)
        <h4 class="p-1 text-center rounded badge-success">
            <div class="ml-2"><i class="fas fa-file"></i> ACTIVO</div>
        </h4>
    @elseif ($contrato->estatus == 2)
        <h4 class="p-1 text-center rounded badge-danger">
            <div class="ml-2"><i class="fas fa-file-excel"></i> ANULADO</div>
        </h4>
    @elseif ($contrato->estatus == 3)
        <h4 class="p-1 text-center rounded badge-danger">
            <div class="ml-2"><i class="fas fa-file-excel"></i> SUSPENDIDO</div>
        </h4>
    @elseif ($contrato->estatus == 4)
        <h4 class="p-1 text-center rounded badge-warning">
            <div class="ml-2"><i class="fas fa-file"></i> EXTERNO</div>
        </h4>
    @endif

    <!-- MODAL ANULAR-->
    @if ($contrato->estatus != 2)
        <x-button class="btn-block btn-danger" wire:click="$set('anularContratoModal',true)">
            <i class="fas fa-file-excel"></i> ANULAR
        </x-button>
    @endif
    <!-- END ANULAR -->

    <x-confirmation-modal wire:model="anularContratoModal">
        <x-slot name="title">
            {{ __('Anular contrato') }}
        </x-slot>

        <x-slot name="content">
            ¿Deseas Anular este contrato?
        </x-slot>

        <x-slot name="footer">
            <x-secondary-button wire:click="$set('anularContratoModal',false)" wire:loading.attr="disabled">
                {{ __('Cancelar') }}
            </x-secondary-button>
            <x-danger-button wire:click="$set('anularContratoComentario',true)" wire:loading.attr="disabled">
                {{ __('Anular') }}
            </x-danger-button>
        </x-slot>

    </x-confirmation-modal>
    <!-- END MODAL ANULAR -->

    <x-dialog-modal wire:model="anularContratoComentario" maxWidth="lg">
        <x-slot name="title">
            {{ __('Observación') }}
        </x-slot>
        <x-slot name="content">
            <div class="form-row">
                <div class="col-md-12">
                    <div class="form-row ">
                        <div class="col-md">
                            <div class="form-group">
                                <x-label for="observacion" :value="__('Descripción')" />
                                <textarea wire:model="observacion" rows="3" name="observacion" class="form-control" required=""
                                    autocomplete="off"></textarea>
                                <x-input-error for="observacion" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </x-slot>

        <x-slot name="footer">
            <x-secondary-button wire:click="$set('anularContratoComentario', false)" wire:loading.attr="disabled">
                {{ __('Close') }}
            </x-secondary-button>
            <x-button wire:click="anularContrato" wire:loading.attr="disabled">
                {{ __('Save') }}
            </x-button>
        </x-slot>
    </x-dialog-modal>
</div>
