<div>
    <x-dialog-modal wire:model="modalEditNormativa" maxWidth="xl">
        <x-slot name="title">
            {{ __('Editar normativa') }}
        </x-slot>
        <x-slot name="content">
            <div class="container">
                @if (session()->has('message'))
                    <div class="alert alert-success"><i class="fas fa-info-circle"></i> {{ session('message') }}</div>
                @endif

                <div class="row">
                    <div class="col-12">
                        <x-adminlte-textarea wire:model='descripcion' name="descripcion" label="Descripcion" rows=5 label-class="text-dark"
                            igroup-size="sm" placeholder="Descripcion">
                            <x-slot name="prependSlot">
                                <div class="input-group-text bg-dark">
                                    <i class="fas fa-lg fa-file-alt text-warning"></i>
                                </div>
                            </x-slot>
                        </x-adminlte-textarea>
                    </div>
                </div>
            </div>
        </x-slot>
        <x-slot name="footer">
            <x-button class="btn btn-secondary mt-2" wire:click="$set('modalEditNormativa',false)">
                {{ __('Cerrar') }}
            </x-button>
            <x-button class="btn btn-primary mt-2" wire:click="save"><i class="fas fa-save"></i>
                {{ __('Guardar') }}
            </x-button>
        </x-slot>
    </x-dialog-modal>
</div>
