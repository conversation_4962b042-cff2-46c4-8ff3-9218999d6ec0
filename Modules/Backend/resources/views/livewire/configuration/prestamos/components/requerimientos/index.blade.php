<div>
    <livewire:backend::configuration.prestamos.components.requerimientos.create :prestamo="$prestamo" />

    <div class="card card-secondary mt-2">
        <div class="card-header">
            <h3 class="card-title">Requerimientos</h3>
        </div>
        <div class="card-body">
            <div class="form-row">
                <div class="col-md-12">
                    <div class="form-row">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr class="bg-darks">
                                    <th>#</th>
                                    <th width="93%">Descripcion</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($requerimientos as $requerimiento)
                                    <tr wire:key="{{ $requerimiento->id }}">
                                        <th>{{ $loop->iteration }}</th>
                                        <td>{{ $requerimiento->descripcion }}</td>
                                        <td>
                                            <x-button class="btn btn-primary text-uppercase btn-sm"
                                                id="edit{{ $requerimiento->id }}" name="edit{{ $requerimiento->id }}"
                                                wire:click="edit({{ $requerimiento->id }})">
                                                <i class="fas fa-edit"></i>
                                            </x-button>
                                            <x-button class="btn btn-danger text-uppercase btn-sm"
                                                wire:click="destroy({{ $requerimiento->id }})">
                                                <i class="fas fa-trash"></i>
                                            </x-button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="3">No hay requerimientos</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                <livewire:backend::configuration.prestamos.components.requerimientos.edit />
                <livewire:backend::configuration.prestamos.components.requerimientos.destroy />
            </div>
        </div>
    </div>
</div>
