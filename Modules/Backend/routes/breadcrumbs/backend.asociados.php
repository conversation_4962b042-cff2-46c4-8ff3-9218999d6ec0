<?php // routes/breadcrumbs.php

// Note: <PERSON><PERSON> will automatically resolve `Breadcrumbs::` without
// this import. This is nice for IDE syntax and refactoring.
use Diglactic\Breadcrumbs\Breadcrumbs;

// This import is also not required, and you could replace `BreadcrumbTrail $trail`
//  with `$trail`. This is nice for IDE type checking and completion.
use Diglactic\Breadcrumbs\Generator as BreadcrumbTrail;

Breadcrumbs::for('admin.asociados.index', function (BreadcrumbTrail $trail) {
    $trail->parent('admin');
    $trail->push('Listado de asociados', route('admin.asociados.index'));
});

Breadcrumbs::for('admin.asociados.edit', function (BreadcrumbTrail $trail, $id) {
    $trail->parent('admin.asociados.index');
    $trail->push('Editar #'.$id, route('admin.asociados.edit', $id));
});
