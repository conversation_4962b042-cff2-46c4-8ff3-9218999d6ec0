<?php // routes/breadcrumbs.php

// Note: <PERSON><PERSON> will automatically resolve `Breadcrumbs::` without
// this import. This is nice for IDE syntax and refactoring.
use Diglactic\Breadcrumbs\Breadcrumbs;

// This import is also not required, and you could replace `BreadcrumbTrail $trail`
//  with `$trail`. This is nice for IDE type checking and completion.
use Diglactic\Breadcrumbs\Generator as BreadcrumbTrail;

Breadcrumbs::for('admin.solicitudes.prestamos.index', function (BreadcrumbTrail $trail) {
    $trail->parent('admin');
    $trail->push('Solicitudes', route('admin.solicitudes.prestamos.index'));
});

Breadcrumbs::for('admin.solicitudes.prestamos.create', function (BreadcrumbTrail $trail) {
    $trail->parent('admin.solicitudes.prestamos.index');
    $trail->push('Nueva solicitud', route('admin.solicitudes.prestamos.create'));
});


Breadcrumbs::for('admin.solicitudes.prestamos.show', function (BreadcrumbTrail $trail, $id) {
    $trail->parent('admin.solicitudes.prestamos.index');
    $trail->push('Solicitud #'.$id->idsolicitud, route('admin.solicitudes.prestamos.show', $id));
});
