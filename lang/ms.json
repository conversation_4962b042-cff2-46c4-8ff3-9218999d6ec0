{"(and :count more error)": "(dan :count lagi ralat)", "(and :count more errors)": "(dan :count lagi ralat)", "A fresh verification link has been sent to your email address.": "<PERSON><PERSON><PERSON> pengesahan baharu telah dihantar ke alamat e-mel anda.", "A new verification link has been sent to the email address you provided in your profile settings.": "<PERSON><PERSON><PERSON> pengesahan baharu telah dihantar ke alamat e-mel yang anda berikan dalam tetapan profil anda.", "A new verification link has been sent to your email address.": "<PERSON><PERSON><PERSON> pengesahan baharu telah dihantar ke alamat e-mel anda.", "A Timeout Occurred": "Tamat Masa Berlaku", "Accept Invitation": "<PERSON><PERSON><PERSON>", "Accepted": "Diterima", "Add": "Tambah", "Add a new team member to your team, allowing them to collaborate with you.": "<PERSON><PERSON><PERSON> ahli pasukan baharu pada pasukan anda, membole<PERSON>kan mereka bekerjasama dengan anda.", "Add additional security to your account using two factor authentication.": "Tambahkan keselamatan tambahan pada akaun anda menggunakan pengesahan dua faktor.", "Add Team Member": "Tambah Ahli Pasukan", "Added.": "Ditambah.", "Administrator": "Pentadbir", "Administrator users can perform any action.": "Pengguna pentadbir boleh melakukan sebarang tindakan.", "All of the people that are part of this team.": "<PERSON><PERSON><PERSON> orang yang menjadi sebahagian daripada pasukan ini.", "All rights reserved.": "Hak cipta terpeli<PERSON>.", "Already registered?": "Sudah mendaftar?", "Already Reported": "<PERSON><PERSON> Dilaporkan", "API Token": "Token API", "API Token Permissions": "API Tanda Kebenaran", "API Tokens": "API Token", "API tokens allow third-party services to authenticate with our application on your behalf.": "API token membenarkan pihak ketiga perkhidmatan untuk mengesahkan dengan permohonan kami di pihak anda.", "Are you sure you want to delete this team? Once a team is deleted, all of its resources and data will be permanently deleted.": "<PERSON><PERSON><PERSON> anda pasti anda mahu untuk memadamkan pasukan ini? Sekali pasukan itu dihapuskan, semua sumber dan data akan dipadam selamanya.", "Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.": "<PERSON><PERSON><PERSON> anda pasti anda mahu untuk menghilangkan anda akaun? <PERSON><PERSON><PERSON> akaun anda yang <PERSON>, semua sumber dan data akan dipadam selamanya. <PERSON><PERSON> ma<PERSON>kkan password untuk mengkonfirmasi anda ingin memadamkan akaun anda.", "Are you sure you would like to delete this API token?": "<PERSON><PERSON><PERSON><PERSON> anda yakin anda ingin memadamkan API ini tanda?", "Are you sure you would like to leave this team?": "<PERSON><PERSON><PERSON><PERSON> anda yakin anda ingin meninggalkan pasukan ini?", "Are you sure you would like to remove this person from the team?": "<PERSON><PERSON><PERSON><PERSON> anda yakin anda ingin mengeluarkan orang ini dari tim?", "Bad Gateway": "<PERSON><PERSON><PERSON>", "Bad Request": "<PERSON><PERSON><PERSON><PERSON> buruk", "Bandwidth Limit Exceeded": "<PERSON>", "Before continuing, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.": "Sebel<PERSON> men<PERSON>, b<PERSON><PERSON><PERSON>h anda mengesahkan alamat e-mel anda dengan mengklik pautan yang baru kami e-mel kepada anda? <PERSON>ka anda tidak menerima e-mel itu, kami dengan senang hati akan menghantar satu lagi kepada anda.", "Before proceeding, please check your email for a verification link.": "<PERSON><PERSON><PERSON> men<PERSON>, sila semak e-mel anda untuk mendapatkan pautan penges<PERSON>.", "Browser Sessions": "<PERSON><PERSON><PERSON>", "Cancel": "Membatalkan", "Click here to re-send the verification email.": "Klik di sini untuk menghantar semula e-mel pengesahan.", "click here to request another": "klik di sini untuk mohon yang lain", "Client Closed Request": "Permintaan <PERSON>", "Close": "<PERSON><PERSON>", "Code": "Kod", "Confirm": "Mengesahkan", "Confirm Password": "<PERSON><PERSON><PERSON>", "Conflict": "Konflik", "Connection Closed Without Response": "Sambungan Tertutup Tanpa Tindak Balas", "Connection Timed Out": "Sambungan Tamat Masa", "Continue": "teruskan", "Create": "Mewujud<PERSON>", "Create a new team to collaborate with others on projects.": "Me<PERSON>uat yang baru pasukan untuk bekerjasama dengan orang lain di projek.", "Create Account": "<PERSON><PERSON><PERSON>", "Create API Token": "Membuat API Token", "Create New Team": "Membuat Pasukan Baru", "Create Team": "Membuat Pasukan", "Created": "<PERSON><PERSON><PERSON>", "Created.": "<PERSON><PERSON><PERSON>.", "Current Password": "<PERSON><PERSON><PERSON>", "Dashboard": "<PERSON><PERSON><PERSON>", "Delete": "Padamkan", "Delete Account": "<PERSON><PERSON>", "Delete API Token": "Padamkan API Token", "Delete Team": "<PERSON><PERSON><PERSON><PERSON>", "Disable": "Lumpuhkan", "Done.": "Selesai.", "Edit Profile": "Menyunting Profail", "Editor": "Editor", "Editor users have the ability to read, create, and update.": "Editor pengg<PERSON> mempunyai keupayaan untuk membaca, memb<PERSON><PERSON>, dan kini.", "Email": "E-mel", "Email Address": "<PERSON><PERSON><PERSON>", "Email Password Reset Link": "E-mel Pa<PERSON>n <PERSON>", "Enable": "Membolehkan", "Ensure your account is using a long, random password to stay secure.": "Memastikan akaun anda menggunakan yang lama, rawak kata laluan untuk yang selamat.", "Expectation Failed": "<PERSON><PERSON><PERSON>", "Failed Dependency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Finish enabling two factor authentication.": "<PERSON><PERSON><PERSON> mendayakan pengesahan dua faktor.", "For your security, please confirm your password to continue.": "Untuk keselamatan anda, sila sahkan anda kata laluan untuk terus.", "Forbidden": "Dilarang", "Forgot your password?": "Lupa password anda?", "Forgot Your Password?": "Lupa kata laluan anda?", "Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.": "Lupa password anda? Tidak ada masalah. <PERSON><PERSON> be<PERSON>hu kami alamat email anda dan kami akan email anda kata laluan semula link yang akan membenarkan anda untuk memilih satu yang baru.", "Found": "Dijumpai", "Gateway Timeout": "<PERSON><PERSON><PERSON>", "Go to page :page": "<PERSON><PERSON> ke halaman :page", "Gone": "hilang", "Great! You have accepted the invitation to join the :team team.": "<PERSON><PERSON>! Anda telah menerima undangan untuk menyertai :team pasukan.", "Hello!": "<PERSON><PERSON>!", "HTTP Version Not Supported": "Versi HTTP Tidak Disokong", "I agree to the :terms_of_service and :privacy_policy": "<PERSON><PERSON> be<PERSON> untuk :terms_of_service dan :privacy_policy", "I'm a teapot": "<PERSON>a teko", "If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below; however, this list may not be exhaustive. If you feel your account has been compromised, you should also update your password.": "<PERSON><PERSON> per<PERSON>, anda mungkin keluar dari semua dari anda yang lain pelayar sesi-sesi di semua perangkat anda. <PERSON>ber<PERSON> anda baru-baru ini sesi di bawah ini, namun, ini senarai mungkin tidak lengkap. <PERSON><PERSON> anda merasa akaun anda telah dikompromikan, anda harus juga <PERSON> anda kata laluan.", "If you already have an account, you may accept this invitation by clicking the button below:": "<PERSON>ka kau sudah mempun<PERSON>i akaun, kau mungkin menerima undangan ini dengan mengklik tombol di bawah:", "If you did not create an account, no further action is required.": "<PERSON><PERSON> anda tidak membuat akaun, tiada tindakan lanjut dip<PERSON>an.", "If you did not expect to receive an invitation to this team, you may discard this email.": "<PERSON>ka anda tidak mengharapkan untuk menerima undangan untuk tim ini, anda mungkin membuang e-mel ini.", "If you did not receive the email": "<PERSON><PERSON> anda tidak menerima e-mel tersebut", "If you did not request a password reset, no further action is required.": "<PERSON><PERSON> anda tidak meminta tetapan semula kata la<PERSON>an, tiada tindakan lan<PERSON> dip<PERSON>.", "If you do not have an account, you may create one by clicking the button below. After creating an account, you may click the invitation acceptance button in this email to accept the team invitation:": "<PERSON>ka anda tidak mempunyai akaun, anda boleh membuat satu dengan mengklik tombol di bawah. <PERSON><PERSON><PERSON> membuat akaun, anda mungkin klik jemputan penerimaan butang di e-mel ini untuk menerima pasukan jemputan:", "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:": "Sekiranya anda menghadapi masalah klik butang \":actionText\", salin dan tampal URL di bawah\nke pelayar web anda:", "IM Used": "IM <PERSON>", "Insufficient Storage": "Storan Tidak Mencukupi", "Internal Server Error": "<PERSON><PERSON>", "Invalid JSON was returned from the route.": "JSON yang tidak sah telah dikembalikan daripada laluan.", "Invalid SSL Certificate": "Sijil SSL tidak sah", "Last active": "Terakhir aktif", "Last used": "<PERSON><PERSON><PERSON>", "Leave": "Meninggalkan", "Leave Team": "Meninggalkan Pasukan", "Length Required": "<PERSON><PERSON><PERSON>", "Locked": "Terkun<PERSON>", "Log in": "<PERSON><PERSON>", "Log Out": "Log keluar", "Log Out Other Browser Sessions": "Log Out <PERSON>", "Login": "Log masuk", "Logout": "Log keluar", "Loop Detected": "Gelung Dikesan", "Maintenance Mode": "<PERSON><PERSON>", "Manage Account": "<PERSON><PERSON><PERSON>", "Manage and log out your active sessions on other browsers and devices.": "<PERSON><PERSON><PERSON> dan anda log out sesi aktif pada pelayar lain dan alat-alat.", "Manage API Tokens": "Mengurus API Token", "Manage Role": "<PERSON><PERSON><PERSON>", "Manage Team": "<PERSON><PERSON><PERSON>", "Method Not Allowed": "<PERSON><PERSON><PERSON>", "Misdirected Request": "<PERSON><PERSON><PERSON><PERSON>", "Moved Permanently": "<PERSON><PERSON><PERSON><PERSON> secara tetap", "Multi-Status": "Berbilang Status", "Multiple Choices": "Pelbagai Pilihan", "Name": "<PERSON><PERSON>", "Network Authentication Required": "<PERSON><PERSON><PERSON>", "Network Connect Timeout Error": "<PERSON><PERSON>", "Network Read Timeout Error": "<PERSON><PERSON> Ma<PERSON>", "New Password": "<PERSON><PERSON>", "No Content": "Tiada isi kandungan", "Non-Authoritative Information": "Maklumat Tidak Berwibawa", "Not Acceptable": "Tidak boleh diterima", "Not Extended": "Tidak Dipanjangkan", "Not Found": "Tidak <PERSON>", "Not Implemented": "Tidak <PERSON>", "Not Modified": "Tidak Diubahsuai", "of": "dari", "OK": "okey", "Once a team is deleted, all of its resources and data will be permanently deleted. Before deleting this team, please download any data or information regarding this team that you wish to retain.": "Sekali pasukan itu <PERSON>, semua sumber dan data akan dipadam selamanya. Sebelum memotong pasukan ini, sila turun apa-apa data atau maklumat mengenai ini pasukan yang anda ingin menyimpan.", "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.": "<PERSON><PERSON><PERSON> akaun anda yang <PERSON>, semua sumber dan data akan dipadam selamanya. Sebelum memotong akaun anda, sila turun apa-apa data atau maklumat yang anda ingin menyimpan.", "Origin Is Unreachable": "Asal Tidak Dapat Dicapai", "Page Expired": "Halaman Tamat Tempoh", "Pagination Navigation": "Muka Surat Navigasi", "Partial Content": "Kandungan Separa", "Password": "<PERSON><PERSON>", "Payload Too Large": "Muatan Terlal<PERSON>", "Payment Required": "pembayaran diperlukan", "Pending Team Invitations": "Belum Selesai Pasukan Jemputan", "Permanent Redirect": "U<PERSON> hala Ke<PERSON>", "Permanently delete this team.": "Memadamkan pasukan ini.", "Permanently delete your account.": "<PERSON><PERSON><PERSON><PERSON> akaun anda.", "Permissions": "<PERSON><PERSON><PERSON>", "Photo": "Foto", "Please click the button below to verify your email address.": "Sila klik butang di bawah untuk mengesahkan alamat e-mel anda.", "Please confirm access to your account by entering one of your emergency recovery codes.": "<PERSON>la sahkan akses ke account anda dengan memasuki salah satu kecemasan anda pemulihan kod.", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "Sila sahkan akses ke account anda dengan memasukkan kod pengesahan disediakan oleh anda pengesah permohonan.", "Please confirm your password before continuing.": "<PERSON>la sahkan kata laluan anda sebelum men<PERSON>kan.", "Please copy your new API token. For your security, it won't be shown again.": "Tolong di copy baru anda token API. Untuk keselamatan anda, ia tidak akan menunjukkan lagi.", "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.": "<PERSON><PERSON> ma<PERSON>kkan password untuk mengkonfirmasi anda ingin keluar dari pelayar lain sesi-sesi di semua perangkat anda.", "Please provide the email address of the person you would like to add to this team.": "<PERSON><PERSON> member<PERSON>n alamat e-mel orang yang anda ingin menambahkan pasukan ini.", "Precondition Failed": "<PERSON><PERSON><PERSON><PERSON>", "Precondition Required": "<PERSON><PERSON><PERSON><PERSON>", "Privacy Policy": "<PERSON><PERSON>", "Processing": "Memproses", "Profile": "Profil", "Profile Information": "Maklumat Profil", "Proxy Authentication Required": "Pengesahan Proksi <PERSON>", "Railgun Error": "Railgun Rail", "Range Not Satisfiable": "Julat Tidak Memuaskan", "Recovery Code": "<PERSON><PERSON><PERSON><PERSON>", "Regards": "<PERSON><PERSON>", "Regenerate Recovery Codes": "<PERSON><PERSON><PERSON>", "Register": "<PERSON><PERSON><PERSON>", "Remember me": "<PERSON>gat saya", "Remember Me": "<PERSON><PERSON>", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Remove Photo": "<PERSON><PERSON><PERSON><PERSON>", "Remove Team Member": "<PERSON>luark<PERSON>", "Request Header Fields Too Large": "Minta <PERSON>pal<PERSON> Te<PERSON>al<PERSON>", "Request Timeout": "<PERSON><PERSON><PERSON><PERSON> tamat masa", "Resend Verification Email": "<PERSON><PERSON>", "Reset Content": "Tetap<PERSON>", "Reset Password": "Te<PERSON><PERSON>", "Reset Password Notification": "Tetap Se<PERSON> Kat<PERSON>", "results": "keputusan", "Retry With": "Cuba Semula <PERSON>", "Role": "<PERSON><PERSON><PERSON>", "Save": "Simpan", "Saved.": "Disimpan.", "See Other": "<PERSON><PERSON>-lain", "Select A New Photo": "<PERSON><PERSON><PERSON>", "Send Password Reset Link": "Hantar <PERSON>n <PERSON>", "Server Error": "<PERSON><PERSON><PERSON>", "Service Unavailable": "Perkhidmatan tidak tersedia", "Session Has Expired": "Sesi Telah Tamat", "Setup Key": "<PERSON><PERSON><PERSON>", "Show Recovery Codes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Showing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSL Handshake Failed": "Jabat Tangan SSL Gagal", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "Kedai ini pemulihan kod dalam kata laluan selamat pengurus. <PERSON><PERSON>a boleh digunakan untuk mendapatkan akses ke account anda jika anda dua faktor pengesahan peranti hilang.", "Switch Teams": "<PERSON><PERSON><PERSON>", "Switching Protocols": "<PERSON><PERSON><PERSON>", "Team Details": "<PERSON><PERSON><PERSON>", "Team Invitation": "<PERSON><PERSON><PERSON>", "Team Members": "<PERSON><PERSON>", "Team Name": "<PERSON><PERSON>", "Team Owner": "Pemilik <PERSON>kan", "Team Settings": "<PERSON><PERSON>", "Temporary Redirect": "Ubah hala Sementara", "Terms of Service": "<PERSON><PERSON><PERSON>", "The :attribute must be a valid role.": "Itu :attribute mesti peranan yang sah.", "The :attribute must be at least :length characters and contain at least one number.": ":Attribute perlu sekurang-kurangnya :length aksara dan mengandungi sekurang-kurangnya satu nombor.", "The :attribute must be at least :length characters and contain at least one special character and one number.": "Itu :attribute perlu sekurang-kurangnya :length aksara dan mengandungi sekurang-kurangnya satu ciri-ciri khas dan nombor satu.", "The :attribute must be at least :length characters and contain at least one special character.": "Itu :attribute perlu sekurang-kurangnya :length aksara dan mengandungi sekurang-kurangnya satu ciri-ciri khas.", "The :attribute must be at least :length characters and contain at least one uppercase character and one number.": "Itu :attribute perlu sekurang-kurangnya :length aksara dan mengandungi sekurang-kurangnya satu huruf besar watak dan nombor satu.", "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.": "Itu :attribute perlu sekurang-kurangnya :length aksara dan mengandungi sekurang-kurangnya satu huruf besar watak dan salah satu ciri-ciri khas.", "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.": "Itu :attribute perlu sekurang-kurangnya :length aksara dan mengandungi sekurang-kurangnya satu huruf besar watak, salah nombor, dan salah satu ciri-ciri khas.", "The :attribute must be at least :length characters and contain at least one uppercase character.": "Itu :attribute perlu sekurang-kurangnya :length aksara dan mengandungi sekurang-kurangnya satu huruf besar karakter.", "The :attribute must be at least :length characters.": "Itu :attribute perlu sekurang-kurangnya :length aksara.", "The given data was invalid.": "Data yang diberikan adalah tidak sah.", "The password is incorrect.": "<PERSON>a laluan tidak betul.", "The provided password does not match your current password.": "<PERSON> disediakan kata laluan tidak sepadan kata laluan anda.", "The provided password was incorrect.": "<PERSON> disediakan kata laluan adalah tidak betul.", "The provided two factor authentication code was invalid.": "<PERSON> disediakan dua faktor kod pengesahan itu tidak sah.", "The provided two factor recovery code was invalid.": "Kod pemulihan dua faktor yang diberikan adalah tidak sah.", "The response is not a streamed response.": "Tanggapan itu bukan respons yang distrim.", "The response is not a view.": "Tanggapan bukan pandangan.", "The team's name and owner information.": "Pa<PERSON>kan itu nama dan pemilik maklumat.", "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.": "Orang-orang ini telah diundang untuk anda pasukan dan telah menghantar jemputan e-mel. <PERSON><PERSON><PERSON> mungkin akan bergabung dengan pasukan dengan menerima e-mel jemputan.", "This device": "Peranti ini", "This is a secure area of the application. Please confirm your password before continuing.": "Ini adalah kawasan yang selamat permohonan. Sila sahkan kata laluan anda sebelum melanjutkan.", "This password does not match our records.": "<PERSON>a laluan ini tidak sepadan rekod kami.", "This password reset link will expire in :count minutes.": "<PERSON><PERSON> la<PERSON>an semula link akan tamat dalam :count minit.", "This user already belongs to the team.": "Pengguna ini sudah menjadi milik kepada pasukan.", "This user has already been invited to the team.": "Pengguna ini telah diundang untuk pasukan.", "to": "untuk", "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application or enter the setup key and provide the generated OTP code.": "Untuk menyelesaikan mendayakan pengesahan dua faktor, imbas kod QR berikut menggunakan aplikasi pengesah telefon anda atau masukkan kekunci persediaan dan berikan kod OTP yang dijana.", "Toggle navigation": "Togol navigasi", "Token Name": "<PERSON><PERSON>", "Too Early": "<PERSON><PERSON><PERSON><PERSON> awal", "Too Many Requests": "Terlalu Banyak Permintaan", "Two Factor Authentication": "<PERSON><PERSON>", "Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application or enter the setup key.": "Pengesahan dua faktor kini didayakan. Imbas kod QR berikut menggunakan aplikasi pengesah telefon anda atau masukkan kekunci persediaan.", "Unauthorized": "<PERSON><PERSON><PERSON>", "Unavailable For Legal Reasons": "Tidak Tersedia Atas Sebab Undang-undang", "Unknown": "Tidak diketahui", "Unknown Error": "<PERSON><PERSON> t<PERSON>", "Unprocessable Entity": "Entiti Tidak Boleh Diproses", "Unsupported Media Type": "Jenis Media Tidak Disokong", "Update Password": "<PERSON><PERSON>", "Update your account's profile information and email address.": "<PERSON><PERSON> akaun anda maklumat profil dan alamat e-mel.", "Upgrade Required": "Peningkatan Diperlukan", "URI Too Long": "URI Terlalu Panjang", "Use a recovery code": "<PERSON><PERSON><PERSON> kod pemulihan", "Use an authentication code": "<PERSON><PERSON><PERSON> kod pen<PERSON>", "Use Proxy": "<PERSON><PERSON><PERSON>", "Variant Also Negotiates": "<PERSON><PERSON> <PERSON><PERSON>", "Verify Email Address": "<PERSON><PERSON><PERSON><PERSON>", "Verify Your Email Address": "Mengesahkan alamat e-mel anda", "We were unable to find a registered user with this email address.": "Kita tidak dapat mencari pengguna berdaftar dengan alamat e-mel ini.", "Web Server is Down": "Pelayan <PERSON>", "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.": "<PERSON><PERSON><PERSON> dua faktor pengesahan ad<PERSON>, kau akan diminta untuk yang selamat, tanda rawak dalam pengesahan. <PERSON>a mungkin mendapatkan ini tanda dari anda Google telefon Pengesah permohonan.", "Whoops!": "Alamak!", "Whoops! Something went wrong.": "Whoops! Ada sesuatu yang tidak beres.", "You are logged in!": "Anda telah log masuk!", "You are receiving this email because we received a password reset request for your account.": "Anda menerima e-mel ini kerana kami menerima permintaan tetap semula kata laluan untuk akaun anda.", "You have been invited to join the :team team!": "<PERSON>a telah diundang untuk bergabung dengan :team pasukan!", "You have enabled two factor authentication.": "<PERSON>a telah diakti<PERSON>kan dua faktor penges<PERSON>.", "You have not enabled two factor authentication.": "Anda tidak membolehkan dua faktor penges<PERSON>.", "You may accept this invitation by clicking the button below:": "<PERSON><PERSON> boleh menerima jemputan ini dengan mengklik butang di bawah:", "You may delete any of your existing tokens if they are no longer needed.": "Anda mungkin padam apa-apa ada tanda-tanda jika mereka tidak lagi diperlukan.", "You may not delete your personal team.": "<PERSON>a mungkin tidak padam pasukan peribadi anda.", "You may not leave a team that you created.": "Anda mungkin tidak mening<PERSON>kan pasukan yang anda buat.", "Your email address is unverified.": "<PERSON><PERSON><PERSON> e-mel anda tidak disahkan."}