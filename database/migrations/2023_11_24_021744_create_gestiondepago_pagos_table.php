<?php

use App\Models\Asociado;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gestiondepago_pagos', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Asociado::class)
                ->cascadeOnDelete()
                ->cascadeOnUpdate();
            $table->string('codigo_trans');
            $table->string('referencia');
            $table->decimal('monto', 12, 2);
            $table->string('banco_emisor');
            $table->string('banco_receptor');
            $table->date('fecha_pago');

            $table->string('descripcion');
            $table->smallInteger('usuario');

            $table->string('aceptado_por')->nullable()->comment('usuario que aceptó la solicitud');
            $table->date('fecha_aceptado')->nullable()->comment('fecha de aceptacion');
            $table->string('aprobado_por')->nullable()->comment('usuario que aceptó la solicitud');
            $table->date('fecha_aprobado')->nullable()->comment('fecha de aceptacion');
            $table->string('negado_por')->nullable()->comment('usuario que negó la solicitud');
            $table->date('fecha_negado')->nullable()->comment('fecha de aceptacion');
            $table->text('comentario')->nullable()->comment('comentario de la solicitud');
            $table->softDeletes();
            $table->timestamps();

            $table->unsignedBigInteger('estatus', false);
            $table->foreign('estatus')
                ->references('codigo')
                ->on('config_tools')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gestiondepago_pagos');
    }
};
