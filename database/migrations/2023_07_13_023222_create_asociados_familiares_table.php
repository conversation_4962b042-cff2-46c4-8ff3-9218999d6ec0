<?php

use App\Models\Asociado;
use App\Models\Familiares;
use App\Models\Parentescos;
use App\Models\FamiliaresTipo;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asociados_familiares', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Asociado::class)
                ->constrained('asociados', 'idasociado')
                ->cascadeOnUpdate()
                ->cascadeOnDelete();

            $table->foreignIdFor(Familiares::class)
                ->constrained('familiares', 'id')
                ->cascadeOnUpdate()
                ->cascadeOnDelete();
            $table->foreignIdFor(Parentescos::class)
                ->constrained('parentescos', 'id')
                ->cascadeOnUpdate()
                ->cascadeOnDelete();
            $table->foreignIdFor(FamiliaresTipo::class)
                ->constrained('familiares_tipos', 'id')
                ->cascadeOnUpdate()
                ->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asociados_familiares');
    }
};
