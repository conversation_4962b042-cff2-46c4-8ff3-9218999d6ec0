<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('familiares', function (Blueprint $table) {
            $table->id();
            $table->string('cedula')->nullable();
            $table->string('nombres', 100);
            $table->string('apellidos', 100);
            $table->integer('sexo')->nullable();;
            $table->date('fecha_nac')->nullable();
            $table->integer('residencia')->nullable();
            $table->text('direccion')->nullable();;
            $table->string('telefono_hab')->nullable();
            $table->string('telefono_cel')->nullable();
            $table->string('telefono_cont')->nullable();
            $table->string('ocupacion')->nullable();
            $table->integer('estado_civil')->nullable();
            $table->integer('estatus')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('familiares');
    }
};
